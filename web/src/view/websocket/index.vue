<template>
  <div class="websocket-demo">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>WebSocket 连接状态</span>
          <WebSocketStatus />
        </div>
      </template>
      
      <div class="status-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="连接状态">
            <el-tag :type="statusTagType">{{ statusText }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="未读消息">
            <el-badge :value="unreadCount" :max="99">
              <el-button size="small">消息</el-button>
            </el-badge>
          </el-descriptions-item>
          <el-descriptions-item label="连接地址">
            <el-text size="small">ws://**************:82/api/msgSocket</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="最后消息时间">
            <el-text size="small">{{ lastMessageTime || '暂无消息' }}</el-text>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="action-buttons">
        <el-button 
          type="primary" 
          :disabled="!isConnected"
          @click="sendTestMessage"
        >
          发送测试消息
        </el-button>
        <el-button 
          type="success" 
          :disabled="isConnected"
          @click="reconnect"
          :loading="isConnecting"
        >
          重新连接
        </el-button>
        <el-button 
          type="danger" 
          :disabled="!isConnected"
          @click="disconnect"
        >
          断开连接
        </el-button>
        <el-button 
          type="info" 
          @click="clearMessages"
        >
          清空消息
        </el-button>
      </div>
    </el-card>

    <el-card class="box-card message-history">
      <template #header>
        <div class="card-header">
          <span>消息历史 (最近{{ messageHistory.length }}条)</span>
          <el-button size="small" @click="clearUnreadCount">清除未读</el-button>
        </div>
      </template>
      
      <div class="message-list" ref="messageListRef">
        <div 
          v-for="(message, index) in messageHistory" 
          :key="index"
          class="message-item"
        >
          <div class="message-header">
            <el-tag size="small" :type="getMessageTypeTag(message.type)">
              类型: {{ message.type }}
            </el-tag>
            <el-text size="small" class="message-time">
              {{ formatTime(message.timestamp) }}
            </el-text>
          </div>
          <div class="message-content">
            <pre>{{ JSON.stringify(message, null, 2) }}</pre>
          </div>
        </div>
        
        <el-empty 
          v-if="messageHistory.length === 0"
          description="暂无消息"
          :image-size="100"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed, ref, nextTick, watch } from 'vue'
import { useWebSocketStore } from '@/pinia/modules/websocket'
import { useUserStore } from '@/pinia/modules/user'
import { ElMessage } from 'element-plus'
import WebSocketStatus from '@/components/WebSocketStatus.vue'

const webSocketStore = useWebSocketStore()
const userStore = useUserStore()
const messageListRef = ref(null)

// 计算属性
const isConnected = computed(() => webSocketStore.isConnected)
const isConnecting = computed(() => webSocketStore.isConnecting)
const connectionStatus = computed(() => webSocketStore.connectionStatus)
const unreadCount = computed(() => webSocketStore.unreadCount)
const messageHistory = computed(() => webSocketStore.messageHistory)
const lastMessage = computed(() => webSocketStore.lastMessage)

const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中'
    case 'disconnected':
    default:
      return '未连接'
  }
})

const statusTagType = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'success'
    case 'connecting':
      return 'warning'
    case 'disconnected':
    default:
      return 'danger'
  }
})

const lastMessageTime = computed(() => {
  if (!lastMessage.value || !lastMessage.value.timestamp) {
    return null
  }
  return formatTime(lastMessage.value.timestamp)
})

// 监听消息变化，自动滚动到底部
watch(messageHistory, () => {
  nextTick(() => {
    if (messageListRef.value) {
      messageListRef.value.scrollTop = messageListRef.value.scrollHeight
    }
  })
}, { deep: true })

// 方法
const sendTestMessage = () => {
  const testMessage = {
    type: 0, // 心跳消息
    content: 'Test message from web admin',
    timestamp: new Date().toISOString()
  }
  
  const success = webSocketStore.sendMessage(testMessage)
  if (success) {
    ElMessage.success('测试消息已发送')
  } else {
    ElMessage.error('发送失败')
  }
}

const reconnect = async () => {
  try {
    await webSocketStore.reconnect(userStore.token)
    ElMessage.success('重连成功')
  } catch (error) {
    ElMessage.error('重连失败: ' + error.message)
  }
}

const disconnect = () => {
  webSocketStore.closeConnection()
  ElMessage.info('连接已断开')
}

const clearMessages = () => {
  webSocketStore.clearMessageHistory()
  ElMessage.info('消息历史已清空')
}

const clearUnreadCount = () => {
  webSocketStore.clearUnreadCount()
  ElMessage.info('未读计数已清除')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

const getMessageTypeTag = (type) => {
  const typeMap = {
    0: 'info',      // 心跳
    1: 'primary',   // 私聊消息
    2: 'success',   // 群聊消息
    3: 'warning',   // 好友申请
    4: 'success',   // 通过好友申请
    5: 'danger',    // 拒绝好友申请
    6: 'danger',    // 删除好友
    23: 'success',  // 用户上线
    24: 'info'      // 用户下线
  }
  return typeMap[type] || 'default'
}
</script>

<style scoped>
.websocket-demo {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.message-history {
  max-height: 600px;
}

.message-list {
  max-height: 500px;
  overflow-y: auto;
}

.message-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fafafa;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-time {
  color: #909399;
}

.message-content {
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.message-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
