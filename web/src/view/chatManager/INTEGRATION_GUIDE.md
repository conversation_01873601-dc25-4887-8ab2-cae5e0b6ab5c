# 聊天系统集成指南

## 快速开始

### 1. 在应用启动时初始化

在你的主应用文件（如 `main.js` 或 `App.vue`）中初始化聊天系统：

```javascript
// main.js 或 App.vue
import { chatManager } from '@/utils/chatManager.js'

// 应用启动时初始化
const initApp = async () => {
  try {
    await chatManager.init()
    console.log('聊天系统初始化成功')
  } catch (error) {
    console.error('聊天系统初始化失败:', error)
  }
}

initApp()
```

### 2. 保存群聊列表

当从接口获取群聊列表后，保存到IndexedDB：

```javascript
// 在获取群聊列表的地方
import { chatManager } from '@/utils/chatManager.js'

const loadGroupList = async () => {
  try {
    // 从接口获取群聊列表
    const response = await getGroupListAPI()
    const groupList = response.data
    
    // 保存到IndexedDB
    await chatManager.saveGroupList(groupList)
    console.log('群聊列表已保存到数据库')
  } catch (error) {
    console.error('保存群聊列表失败:', error)
  }
}
```

### 3. 处理用户点击头像开始私聊

在群聊成员列表或其他地方，当用户点击头像时：

```javascript
// 在用户头像点击事件中
import { chatManager } from '@/utils/chatManager.js'

const onUserAvatarClick = async (userInfo) => {
  try {
    // 开始私聊（会自动添加到私聊列表）
    await chatManager.startPrivateChat({
      id: userInfo.id,
      name: userInfo.name || userInfo.nickname,
      avatar: userInfo.avatar || userInfo.head_img
    })
    
    console.log('私聊已创建')
    
    // 可以在这里跳转到聊天界面
    // navigateToChat(userInfo.id, false) // false表示私聊
  } catch (error) {
    console.error('创建私聊失败:', error)
  }
}
```

### 4. 发送消息

发送群聊或私聊消息：

```javascript
import { chatManager } from '@/utils/chatManager.js'

// 发送群聊消息
const sendGroupMessage = async (groupId, messageContent) => {
  const messageData = {
    id: `msg_${Date.now()}`,
    fromid: getCurrentUserId(), // 获取当前用户ID的函数
    toid: groupId,
    chatid: groupId,
    msg: messageContent,
    typecode2: 0 // 文本消息
  }
  
  try {
    await chatManager.sendGroupMessage(messageData)
    console.log('群聊消息发送成功')
  } catch (error) {
    console.error('发送群聊消息失败:', error)
  }
}

// 发送私聊消息
const sendPrivateMessage = async (userId, messageContent) => {
  const messageData = {
    id: `msg_${Date.now()}`,
    fromid: getCurrentUserId(),
    toid: userId,
    chatid: userId, // 私聊使用用户ID作为chatId
    msg: messageContent,
    typecode2: 0
  }
  
  try {
    await chatManager.sendPrivateMessage(messageData)
    console.log('私聊消息发送成功')
  } catch (error) {
    console.error('发送私聊消息失败:', error)
  }
}
```

### 5. 接收WebSocket消息

在WebSocket消息处理中：

```javascript
import { chatManager } from '@/utils/chatManager.js'

// WebSocket消息接收处理
const onWebSocketMessage = async (messageData) => {
  try {
    // 系统会自动根据typecode判断是群聊还是私聊
    await chatManager.receiveMessage(messageData)
    console.log('消息已保存到数据库')
    
    // 可以在这里触发UI更新
    // updateChatUI(messageData)
  } catch (error) {
    console.error('处理接收消息失败:', error)
  }
}
```

### 6. 获取聊天列表

在聊天列表页面：

```javascript
import { chatManager } from '@/utils/chatManager.js'

const loadChatList = async () => {
  try {
    // 获取所有聊天列表（群聊+私聊）
    const allChats = await chatManager.getAllChats()
    
    // 或者分别获取
    const groupChats = await chatManager.getGroupList()
    const privateChats = await chatManager.getPrivateList()
    
    console.log('聊天列表:', allChats)
    return allChats
  } catch (error) {
    console.error('获取聊天列表失败:', error)
    return []
  }
}
```

### 7. 获取聊天记录

在聊天界面：

```javascript
import { chatManager } from '@/utils/chatManager.js'

const loadChatMessages = async (chatId, isGroup, page = 1) => {
  try {
    let messages
    if (isGroup) {
      messages = await chatManager.getGroupMessages(chatId, page, 20)
    } else {
      messages = await chatManager.getPrivateMessages(chatId, page, 20)
    }
    
    console.log('聊天记录:', messages)
    return messages
  } catch (error) {
    console.error('获取聊天记录失败:', error)
    return []
  }
}
```

## Vue组件集成示例

### 聊天列表组件

```vue
<template>
  <div class="chat-list">
    <div 
      v-for="chat in chatList" 
      :key="chat.id"
      class="chat-item"
      @click="openChat(chat)"
    >
      <img :src="chat.avatar" class="avatar" />
      <div class="chat-info">
        <div class="name">{{ chat.name }}</div>
        <div class="last-message">{{ chat.lastMessage }}</div>
      </div>
      <div class="time">{{ formatTime(chat.timestamp) }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { chatManager } from '@/utils/chatManager.js'

const chatList = ref([])

const loadChatList = async () => {
  try {
    chatList.value = await chatManager.getAllChats()
  } catch (error) {
    console.error('加载聊天列表失败:', error)
  }
}

const openChat = (chat) => {
  const isGroup = chat.type === 'group'
  // 跳转到聊天界面
  // router.push(`/chat/${chat.chatId}?isGroup=${isGroup}`)
}

const formatTime = (timestamp) => {
  return chatManager.formatTime(timestamp)
}

onMounted(() => {
  loadChatList()
})
</script>
```

### 聊天界面组件

```vue
<template>
  <div class="chat-interface">
    <div class="messages">
      <div 
        v-for="message in messages" 
        :key="message.id"
        class="message"
        :class="{ 'own': message.fromid === currentUserId }"
      >
        <div class="content">{{ message.msg }}</div>
        <div class="time">{{ formatTime(message.timestamp) }}</div>
      </div>
    </div>
    
    <div class="input-area">
      <input 
        v-model="inputMessage" 
        @keyup.enter="sendMessage"
        placeholder="输入消息..."
      />
      <button @click="sendMessage">发送</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { chatManager } from '@/utils/chatManager.js'

const props = defineProps({
  chatId: String,
  isGroup: Boolean
})

const messages = ref([])
const inputMessage = ref('')
const currentUserId = ref(null) // 从用户状态获取

const loadMessages = async () => {
  try {
    if (props.isGroup) {
      messages.value = await chatManager.getGroupMessages(props.chatId, 1, 50)
    } else {
      messages.value = await chatManager.getPrivateMessages(props.chatId, 1, 50)
    }
  } catch (error) {
    console.error('加载消息失败:', error)
  }
}

const sendMessage = async () => {
  if (!inputMessage.value.trim()) return
  
  try {
    const messageData = {
      id: `msg_${Date.now()}`,
      fromid: currentUserId.value,
      toid: props.chatId,
      chatid: props.chatId,
      msg: inputMessage.value,
      typecode2: 0
    }
    
    if (props.isGroup) {
      await chatManager.sendGroupMessage(messageData)
    } else {
      await chatManager.sendPrivateMessage(messageData)
    }
    
    inputMessage.value = ''
    await loadMessages() // 重新加载消息
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

const formatTime = (timestamp) => {
  return chatManager.formatTime(timestamp)
}

onMounted(() => {
  loadMessages()
})
</script>
```

## 测试系统

运行测试以验证系统功能：

```javascript
// 在浏览器控制台中运行
import { runQuickTest } from '@/utils/chatTest.js'

// 运行完整测试
runQuickTest().then(result => {
  console.log('测试结果:', result)
})
```

## 注意事项

1. **用户ID获取**：确保能正确获取当前用户ID
2. **WebSocket集成**：在WebSocket消息处理中调用 `chatManager.receiveMessage()`
3. **错误处理**：所有异步操作都要进行错误处理
4. **性能优化**：大量消息时考虑虚拟滚动
5. **数据清理**：定期清理过期数据

## 常见问题

### Q: 如何区分群聊和私聊的消息？
A: 系统通过 `typecode` 字段区分：1为私聊，2为群聊。私聊使用用户ID作为chatId，群聊使用群聊ID作为chatId。

### Q: 消息时间排序是怎样的？
A: 存储时按时间正序（最早在前），显示时可以根据需要调整顺序。系统支持按时间戳排序。

### Q: 如何处理离线消息？
A: 所有消息都存储在IndexedDB中，支持离线访问。重新连接时会自动同步。

### Q: 如何清空聊天记录？
A: 使用 `chatManager.clearMessages(chatId, isGroup)` 清空记录，或使用 `chatManager.deleteChat(chatId, isGroup)` 删除整个聊天。

这个集成指南提供了完整的使用方法，你可以根据实际需求进行调整。
