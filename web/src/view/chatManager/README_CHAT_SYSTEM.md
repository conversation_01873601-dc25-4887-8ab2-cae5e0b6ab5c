# 聊天记录IndexedDB存储系统

## 概述

本系统实现了完整的聊天记录和聊天列表的IndexedDB存储管理，支持群聊和私聊的区分存储，按时间顺序管理聊天记录。

## 核心特性

### 1. 聊天列表管理
- **群聊列表**：从接口获取的群聊列表存储到IndexedDB，chatId使用群聊ID
- **私聊列表**：点击用户头像时动态添加，chatId使用用户ID
- **列表合并**：支持获取合并的聊天列表（群聊+私聊）

### 2. 聊天记录存储
- **时间排序**：按时间顺序存储，最新消息在最后
- **类型区分**：通过typecode区分群聊(2)和私聊(1)
- **分页查询**：支持分页获取聊天记录
- **消息搜索**：支持全局和指定聊天的消息搜索

### 3. 数据结构

#### 消息数据结构
```javascript
{
  id: String,              // 消息ID
  typecode: Number,        // 消息类型：1-私聊，2-群聊
  typecode2: Number,       // 内容类型：0-文本，1-图片，2-语音等
  toid: Number,           // 接收者ID
  fromid: Number,         // 发送者ID
  chatid: Number,         // 聊天对象ID（群聊ID或用户ID）
  t: String,              // 时间戳字符串
  timestamp: Number,      // 时间戳数字
  msg: String,            // 消息内容
  isRedRead: Number,      // 已读状态：0-未读，1-已读
  idDel: Number,          // 删除状态：0-正常，1-已删除
  nickname: String,       // 发送者昵称
  avatar: String,         // 发送者头像
  senderNickname: String, // 群聊中发送者昵称
  senderAvatar: String    // 群聊中发送者头像
}
```

#### 聊天列表数据结构
```javascript
{
  id: String,             // 列表项ID：group_${groupId} 或 private_${userId}
  chatId: Number,         // 聊天对象ID
  name: String,           // 显示名称
  avatar: String,         // 头像URL
  lastMessage: String,    // 最后消息
  lastTime: String,       // 最后消息时间
  timestamp: Number,      // 时间戳
  unreadCount: Number,    // 未读消息数
  type: String,           // 类型：'group' 或 'private'
  typecode: Number,       // 类型码：1-私聊，2-群聊
  isGroup: Boolean        // 是否为群聊
}
```

## 使用方法

### 1. 初始化系统

```javascript
import { chatManager } from '@/utils/chatManager.js'

// 初始化聊天管理器
await chatManager.init()
```

### 2. 群聊管理

```javascript
// 保存群聊列表（从接口获取后调用）
const groupList = [
  {
    id: 1001,
    name: '技术交流群',
    avatar: '/static/group1.jpg'
  }
]
await chatManager.saveGroupList(groupList)

// 获取群聊列表
const groups = await chatManager.getGroupList()

// 发送群聊消息
const groupMessage = {
  id: `msg_${Date.now()}`,
  fromid: currentUserId,
  toid: groupId,
  chatid: groupId,
  msg: '群聊消息内容'
}
await chatManager.sendGroupMessage(groupMessage)

// 获取群聊消息记录
const messages = await chatManager.getGroupMessages(groupId, 1, 20)
```

### 3. 私聊管理

```javascript
// 开始私聊（点击用户头像时调用）
const userInfo = {
  id: 2001,
  name: '张三',
  avatar: '/static/user1.jpg'
}
await chatManager.startPrivateChat(userInfo)

// 获取私聊列表
const privateChats = await chatManager.getPrivateList()

// 发送私聊消息
const privateMessage = {
  id: `msg_${Date.now()}`,
  fromid: currentUserId,
  toid: userId,
  chatid: userId, // 私聊使用用户ID作为chatId
  msg: '私聊消息内容'
}
await chatManager.sendPrivateMessage(privateMessage)

// 获取私聊消息记录
const messages = await chatManager.getPrivateMessages(userId, 1, 20)
```

### 4. WebSocket消息接收

```javascript
// 接收WebSocket消息
const onMessageReceived = async (messageData) => {
  // 系统会自动根据typecode判断是群聊还是私聊
  await chatManager.receiveMessage(messageData)
}
```

### 5. 聊天列表操作

```javascript
// 获取所有聊天列表（群聊+私聊）
const allChats = await chatManager.getAllChats()

// 清空聊天记录（保留聊天列表）
await chatManager.clearMessages(chatId, isGroup)

// 删除聊天（删除记录和列表项）
await chatManager.deleteChat(chatId, isGroup)

// 搜索聊天记录
const results = await chatManager.searchMessages('搜索关键词')
```

## 文件结构

```
web/src/utils/
├── db.js                 # IndexedDB底层操作
├── chatManager.js        # 聊天管理器主类
└── chatUsageExample.js   # 使用示例

web/src/view/chatManager/
├── ChatDialog.vue        # 聊天对话框组件（已集成）
└── README_CHAT_SYSTEM.md # 本说明文档
```

## 核心API

### chatManager 主要方法

| 方法 | 说明 | 参数 |
|------|------|------|
| `init()` | 初始化聊天管理器 | - |
| `saveGroupList(groupList)` | 保存群聊列表 | 群聊数组 |
| `getGroupList()` | 获取群聊列表 | - |
| `startPrivateChat(userInfo)` | 开始私聊 | 用户信息对象 |
| `getPrivateList()` | 获取私聊列表 | - |
| `sendGroupMessage(messageData)` | 发送群聊消息 | 消息对象 |
| `sendPrivateMessage(messageData)` | 发送私聊消息 | 消息对象 |
| `receiveMessage(messageData)` | 接收消息 | 消息对象 |
| `getGroupMessages(groupId, page, size)` | 获取群聊消息 | 群聊ID, 页码, 每页数量 |
| `getPrivateMessages(userId, page, size)` | 获取私聊消息 | 用户ID, 页码, 每页数量 |
| `getAllChats()` | 获取所有聊天列表 | - |
| `searchMessages(keyword, chatId, isGroup)` | 搜索消息 | 关键词, 聊天ID, 是否群聊 |

## 注意事项

1. **chatId区分**：
   - 群聊：使用群聊ID作为chatId
   - 私聊：使用用户ID作为chatId

2. **消息排序**：
   - 存储时按时间正序（最早在前）
   - 显示时最新消息在最前面（类似微信）

3. **数据持久化**：
   - 所有数据存储在IndexedDB中
   - 支持离线访问
   - 按用户ID隔离数据

4. **性能优化**：
   - 支持分页加载
   - 索引优化查询
   - 缓存用户信息

## 集成示例

在Vue组件中使用：

```vue
<script setup>
import { chatManager } from '@/utils/chatManager.js'
import { onMounted } from 'vue'

onMounted(async () => {
  // 初始化聊天系统
  await chatManager.init()
  
  // 加载聊天列表
  const chats = await chatManager.getAllChats()
  console.log('聊天列表:', chats)
})

// 处理消息发送
const sendMessage = async (messageContent, chatId, isGroup) => {
  const messageData = {
    id: `msg_${Date.now()}`,
    fromid: currentUserId,
    toid: chatId,
    chatid: chatId,
    msg: messageContent
  }
  
  if (isGroup) {
    await chatManager.sendGroupMessage(messageData)
  } else {
    await chatManager.sendPrivateMessage(messageData)
  }
}
</script>
```

这个系统完全满足你的需求，提供了完整的群聊和私聊管理功能，支持按时间顺序存储和查询聊天记录。
