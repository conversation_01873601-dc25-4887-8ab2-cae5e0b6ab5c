# 聊天弹窗组件

这是一个基于 Vue 3 + Element Plus 的聊天弹窗组件，模仿了图片中的聊天界面设计。

## 功能特性

- ✅ 响应式设计，支持桌面和移动端
- ✅ 左侧会话列表（群聊和好友切换，竖向排列）
- ✅ 中间聊天区域，支持文本消息
- ✅ 右侧群组成员面板（仅群聊时显示）
- ✅ 消息状态显示（发送中、已发送、已读、失败）
- ✅ 在线状态显示
- ✅ 表情选择器
- ✅ 文件和图片上传（UI已实现，功能待完善）
- ✅ 快捷键支持（Ctrl+Enter 发送消息）

## 组件结构

```
chatManager/
├── ChatDialog.vue              # 主弹窗组件
├── components/
│   ├── ConversationCard.vue    # 会话卡片
│   ├── MessagePanel.vue        # 消息面板
│   ├── MessageItem.vue         # 消息项
│   ├── MessageStatus.vue       # 消息状态
│   ├── MessageEditor.vue       # 消息编辑器
│   └── GroupUserPanel.vue      # 群组成员面板
├── styles/
│   └── chat.scss              # 聊天样式
└── test.vue                   # 测试页面
```

## 使用方法

### 1. 在组件中使用

```vue
<template>
  <div>
    <el-button @click="openChat">打开聊天</el-button>
    
    <ChatDialog 
      v-model="chatVisible" 
      :user="currentUser"
      @close="handleChatClose"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ChatDialog from '@/view/chatManager/ChatDialog.vue'

const chatVisible = ref(false)
const currentUser = ref({
  id: 1,
  name: '张三',
  headImg: '',
  online: true
})

const openChat = () => {
  chatVisible.value = true
}

const handleChatClose = () => {
  chatVisible.value = false
}
</script>
```

### 2. 通过全局事件触发

组件已经集成到主布局中，可以通过全局事件触发：

```javascript
import { emitter } from '@/utils/bus.js'

// 打开聊天弹窗
emitter.emit('openChatDialog', userInfo)

// 或者不传用户信息
emitter.emit('openChatDialog', null)
```

### 3. 在 imuser.vue 中的使用

点击"发送消息"按钮会自动触发聊天弹窗：

```javascript
const sendMessage = (row) => {
  // 触发全局事件，打开聊天弹窗
  import('@/utils/bus.js').then(({ emitter }) => {
    emitter.emit('openChatDialog', row)
  })
}
```

### 4. 在 header/tools.vue 中的使用

点击聊天图标会打开聊天弹窗：

```javascript
const handleMessage = () => {
  // 触发全局事件，打开聊天弹窗
  emitter.emit('openChatDialog', null)
}
```

## 数据格式

### 用户信息格式
```javascript
{
  id: 1,
  name: '用户名',
  headImg: '头像URL',
  online: true // 是否在线
}
```

### 会话信息格式
```javascript
{
  id: 'conversation_id',
  type: 'friend' | 'group',
  name: '会话名称',
  avatar: '头像URL',
  lastMessage: '最后一条消息',
  lastTime: '时间',
  unread: 0, // 未读消息数
  online: true // 是否在线（好友会话）
}
```

### 消息格式
```javascript
{
  id: 'message_id',
  userId: 'user_id',
  nickname: '发送者昵称',
  avatar: '发送者头像',
  content: '消息内容',
  type: 'text' | 'image' | 'file',
  createdAt: new Date(),
  isOwn: false, // 是否是自己发送的
  status: 'sent' | 'sending' | 'read' | 'failed'
}
```

## 样式定制

可以通过修改 `styles/chat.scss` 文件来定制样式，主要包括：

- 弹窗尺寸和位置
- 颜色主题
- 消息气泡样式
- 在线状态指示器
- 响应式断点

## 待完善功能

1. 图片和文件上传功能
2. 消息撤回功能
3. @提及功能
4. 消息搜索功能
5. 聊天记录持久化
6. WebSocket 实时通信
7. 语音和视频通话
8. 群组管理功能

## 注意事项

1. 组件依赖 Element Plus 和 mitt 事件总线
2. 图标使用 Element Plus Icons，已全局注册
3. 当前使用模拟数据，实际使用时需要对接后端 API
4. 响应式设计在移动端会自动调整布局
