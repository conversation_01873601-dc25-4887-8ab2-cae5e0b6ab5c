# 群聊功能使用指南

## 🎯 功能概述

现在系统已经完整实现了群聊列表获取、存储和聊天记录管理功能：

1. **获取群聊列表**：从API获取群聊信息
2. **存储到IndexedDB**：自动保存群聊列表到本地数据库
3. **群聊消息管理**：发送和接收群聊消息，按时间顺序存储

## 🔄 工作流程

### 1. 群聊列表获取和存储

```javascript
// 在 ChatDialog.vue 中的 fetchGroupList 函数
const fetchGroupList = async () => {
  // 1. 调用API获取群聊列表
  const response = await getGroupList(params)
  
  // 2. 解析响应数据
  const groupList = responseData.data.list
  
  // 3. 转换为聊天列表格式
  const groupsData = groupList.map(group => ({
    id: `group_${group.id}`,
    type: 'group',
    name: group.groupName,
    avatar: group.groupHeader || '',
    lastMessage: '暂无消息',
    originalData: group
  }))
  
  // 4. 更新本地显示
  groupConversations.value = groupsData
  
  // 5. 存储到IndexedDB
  await chatManager.saveGroupList(groupList)
}
```

### 2. 群聊消息发送

```javascript
// 在 MessagePanel.vue 中的消息发送逻辑
const sendMessage = async (content, messageType = 0) => {
  // 1. 构建消息数据
  const messageItem = {
    id: `msg_${Date.now()}`,
    fromid: currentUserId,
    toid: groupId,
    chatid: groupId,
    msg: content,
    typecode: 2, // 群聊类型
    typecode2: messageType
  }
  
  // 2. 发送到服务器
  await sendMessageToServer(messageParams)
  
  // 3. 保存到IndexedDB
  await chatManager.sendGroupMessage(messageItem)
  
  // 4. 更新聊天列表
  // 自动触发群聊列表的最后消息更新
}
```

### 3. 群聊消息接收

```javascript
// WebSocket消息接收处理
const handleReceivedMessage = async (data) => {
  // 1. 构建消息对象
  const messageItem = {
    id: data.id,
    fromid: data.fromid,
    toid: data.toid,
    chatid: data.groupID || data.toid,
    msg: decryptedMsg,
    typecode: 2, // 群聊类型
    t: data.t
  }
  
  // 2. 使用聊天管理器保存
  await chatManager.receiveMessage(messageItem)
  
  // 3. 自动更新聊天列表最后消息
}
```

### 4. 群聊消息加载

```javascript
// 加载群聊历史消息
const loadMessages = async () => {
  const isGroup = conversation.type === 'group'
  const chatId = conversation.originalData.id
  
  if (isGroup) {
    // 使用聊天管理器加载群聊消息
    const messages = await chatManager.getGroupMessages(chatId, page, size)
    // 消息按时间正序排列（最早的在前）
  }
}
```

## 📊 数据结构

### 群聊列表数据结构

```javascript
{
  id: "group_1001",           // 界面显示ID
  type: "group",              // 类型：群聊
  name: "技术交流群",          // 群聊名称
  avatar: "/static/group.jpg", // 群聊头像
  lastMessage: "最后一条消息",  // 最后消息内容
  lastTime: "10:30",          // 最后消息时间
  unread: 0,                  // 未读消息数
  online: true,               // 在线状态
  originalData: {             // 原始群聊数据
    id: 1001,
    groupName: "技术交流群",
    groupHeader: "/static/group.jpg",
    createdAt: "2024-01-01T10:00:00Z"
  }
}
```

### 群聊消息数据结构

```javascript
{
  id: "msg_1234567890",       // 消息ID
  fromid: 2001,               // 发送者ID
  toid: 1001,                 // 群聊ID
  chatid: 1001,               // 聊天对象ID（群聊ID）
  msg: "消息内容",            // 消息文本
  typecode: 2,                // 消息类型：2-群聊
  typecode2: 0,               // 内容类型：0-文本，1-图片等
  t: "2024-01-01T10:00:00Z",  // 时间戳字符串
  timestamp: 1704096000000,   // 时间戳数字
  isRedRead: 0,               // 已读状态：0-未读，1-已读
  idDel: 0,                   // 删除状态：0-正常，1-已删除
  nickname: "发送者昵称",      // 发送者昵称
  avatar: "/static/user.jpg", // 发送者头像
  senderNickname: "发送者昵称", // 群聊中发送者昵称
  senderAvatar: "/static/user.jpg" // 群聊中发送者头像
}
```

## 🧪 测试功能

在开发环境下，可以使用以下测试工具：

```javascript
// 在浏览器控制台中运行

// 1. 测试群聊列表存储
await window.testGroupList()

// 2. 测试群聊消息存储
await window.testGroupMessages()

// 3. 测试群聊列表更新
await window.testGroupUpdate()

// 4. 运行所有群聊测试
await window.runGroupTests()

// 5. 清理测试数据
await window.cleanupGroupTests()
```

## 🔍 调试信息

### 查看群聊列表

```javascript
// 获取当前群聊列表
const { chatManager } = await import('@/utils/chatManager.js')
const groups = await chatManager.getGroupList()
console.log('群聊列表:', groups)
```

### 查看群聊消息

```javascript
// 获取特定群聊的消息
const messages = await chatManager.getGroupMessages(1001, 1, 20)
console.log('群聊消息:', messages)
```

### 查看数据库状态

```javascript
// 检查数据库连接状态
await window.checkDB()

// 查看系统整体状态
await window.checkSystemStatus()
```

## 📝 关键特性

### 1. 自动存储
- 获取群聊列表后自动存储到IndexedDB
- 发送/接收消息时自动保存到数据库
- 自动更新聊天列表的最后消息

### 2. 时间排序
- 消息按发送时间正序存储（最早的在前）
- 聊天列表按最后消息时间倒序排列（最新的在前）
- 支持分页加载历史消息

### 3. 数据同步
- 发送消息后立即更新本地数据
- 接收消息时自动同步到数据库
- 聊天列表实时更新最后消息

### 4. 错误处理
- 完善的错误捕获和重试机制
- 备用方案确保功能可用性
- 详细的日志记录便于调试

## 🚀 使用步骤

### 1. 初始化
```javascript
// 应用启动时自动初始化
// 无需手动操作
```

### 2. 获取群聊列表
```javascript
// 在 ChatDialog 组件中
await fetchGroupList()
// 自动获取API数据并存储到IndexedDB
```

### 3. 开始群聊
```javascript
// 选择群聊后自动加载历史消息
// 发送消息会自动保存到数据库
```

### 4. 消息同步
```javascript
// WebSocket接收到消息时自动处理
// 无需手动干预
```

## ⚠️ 注意事项

1. **数据隔离**：每个用户的数据独立存储
2. **版本兼容**：支持数据库版本升级
3. **性能优化**：大量消息时使用分页加载
4. **离线支持**：所有数据本地存储，支持离线查看

## 🔧 故障排除

### 如果群聊列表不显示
1. 检查API接口是否正常
2. 查看浏览器控制台错误信息
3. 运行 `window.testGroupList()` 测试

### 如果消息不保存
1. 检查数据库初始化状态
2. 运行 `window.checkDB()` 检查数据库
3. 查看网络请求是否成功

### 如果时间排序错误
1. 检查消息的时间戳字段
2. 运行 `window.testGroupMessages()` 测试
3. 查看数据库中的实际数据

这个系统现在完全满足你的需求：获取群聊列表后自动存储，开始群聊时自动管理聊天记录，所有数据按时间顺序正确存储和显示。
