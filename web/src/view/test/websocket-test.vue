<template>
  <div class="websocket-test-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>WebSocket 连接测试</span>
          <WebSocketStatus />
        </div>
      </template>
      
      <div class="test-content">
        <el-alert
          title="WebSocket 功能已集成"
          type="success"
          description="WebSocket连接会在用户登录成功后自动建立，连接地址：ws://**************:82/api/msgSocket?token=用户token"
          show-icon
          :closable="false"
        />
        
        <div class="status-section">
          <h3>连接状态</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="连接状态">
              <el-tag :type="statusTagType">{{ connectionStatusText }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="未读消息数">
              <el-badge :value="unreadCount" :max="99">
                <span>{{ unreadCount }}</span>
              </el-badge>
            </el-descriptions-item>
            <el-descriptions-item label="最后消息时间">
              {{ lastMessageTime || '暂无消息' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="actions-section">
          <h3>操作</h3>
          <el-space wrap>
            <el-button 
              type="primary" 
              :disabled="!isConnected"
              @click="sendHeartbeat"
            >
              发送心跳
            </el-button>
            <el-button
              type="success"
              :disabled="isConnected"
              @click="reconnect"
              :loading="isConnecting"
            >
              重新连接
            </el-button>
            <el-button 
              type="info" 
              @click="clearUnread"
            >
              清除未读
            </el-button>
            <el-button
              type="warning"
              @click="clearHistory"
            >
              清空历史
            </el-button>
            <el-button
              type="primary"
              @click="openChatDialog"
            >
              打开聊天对话框
            </el-button>
          </el-space>
        </div>

        <div class="messages-section">
          <h3>消息历史 (最近10条)</h3>
          <div class="message-list">
            <div 
              v-for="(message, index) in recentMessages" 
              :key="index"
              class="message-item"
            >
              <div class="message-header">
                <el-tag size="small" :type="getMessageTypeTag(message.type)">
                  类型: {{ getMessageTypeName(message.type) }}
                </el-tag>
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-content">
                {{ JSON.stringify(message, null, 2) }}
              </div>
            </div>
            
            <el-empty 
              v-if="recentMessages.length === 0"
              description="暂无消息"
              :image-size="80"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 聊天对话框 -->
    <ChatDialog
      v-model="chatDialogVisible"
      :user="currentUser"
      @close="chatDialogVisible = false"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useWebSocketStore } from '@/pinia/modules/websocket'
import { useUserStore } from '@/pinia/modules/user'
import { ElMessage } from 'element-plus'
import WebSocketStatus from '@/components/WebSocketStatus.vue'
import ChatDialog from '@/view/chatManager/ChatDialog.vue'

const webSocketStore = useWebSocketStore()
const userStore = useUserStore()

// 聊天对话框相关
const chatDialogVisible = ref(false)
const currentUser = computed(() => userStore.userInfo)

// 计算属性
const isConnected = computed(() => webSocketStore.isConnected)
const isConnecting = computed(() => webSocketStore.isConnecting)
const connectionStatus = computed(() => webSocketStore.connectionStatus)
const unreadCount = computed(() => webSocketStore.unreadCount)
const messageHistory = computed(() => webSocketStore.messageHistory)
const lastMessage = computed(() => webSocketStore.lastMessage)

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'disconnected':
    default:
      return '未连接'
  }
})

const statusTagType = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'success'
    case 'connecting':
      return 'warning'
    case 'disconnected':
    default:
      return 'danger'
  }
})

const lastMessageTime = computed(() => {
  if (!lastMessage.value?.timestamp) return null
  return formatTime(lastMessage.value.timestamp)
})

const recentMessages = computed(() => {
  return messageHistory.value.slice(-10).reverse()
})

// 方法
const sendHeartbeat = () => {
  const heartbeatMessage = {
    type: 0, // 心跳消息
    content: 'heartbeat',
    timestamp: Date.now()
  }
  
  const success = webSocketStore.sendMessage(heartbeatMessage)
  if (success) {
    ElMessage.success('心跳消息已发送')
  } else {
    ElMessage.error('发送失败')
  }
}

const reconnect = async () => {
  try {
    await webSocketStore.reconnect(userStore.token)
    ElMessage.success('重连成功')
  } catch (error) {
    ElMessage.error('重连失败: ' + error.message)
  }
}

const clearUnread = () => {
  webSocketStore.clearUnreadCount()
  ElMessage.info('未读计数已清除')
}

const clearHistory = () => {
  webSocketStore.clearMessageHistory()
  ElMessage.info('消息历史已清空')
}

const openChatDialog = () => {
  chatDialogVisible.value = true
  ElMessage.success('聊天对话框已打开，可以测试好友聊天功能')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getMessageTypeTag = (type) => {
  const typeMap = {
    0: 'info',      // 心跳
    1: 'primary',   // 私聊消息
    2: 'success',   // 群聊消息
    3: 'warning',   // 好友申请
    4: 'success',   // 通过好友申请
    5: 'danger',    // 拒绝好友申请
    6: 'danger',    // 删除好友
    23: 'success',  // 用户上线
    24: 'info'      // 用户下线
  }
  return typeMap[type] || 'default'
}

const getMessageTypeName = (type) => {
  const typeNames = {
    0: '心跳',
    1: '私聊消息',
    2: '群聊消息',
    3: '好友申请',
    4: '通过好友申请',
    5: '拒绝好友申请',
    6: '删除好友',
    23: '用户上线',
    24: '用户下线'
  }
  return typeNames[type] || `未知类型(${type})`
}
</script>

<style scoped>
.websocket-test-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-section,
.actions-section,
.messages-section {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
}

.status-section h3,
.actions-section h3,
.messages-section h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.message-list {
  max-height: 400px;
  overflow-y: auto;
}

.message-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-time {
  font-size: 12px;
  color: #909399;
}

.message-content {
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
