/**
 * 简化版数据库工具
 * 避免复杂的async/await嵌套，提供更稳定的数据库初始化
 */

// 数据库配置
export const DB_NAME = 'chatDb'
export const DB_VERSION = 5  // 使用固定版本号

// 获取当前用户ID
const getCurrentUserId = () => {
  return localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default'
}

// 获取表名
export const getTableName = () => `chatData_${getCurrentUserId()}`
export const getChatListTableName = () => `chatList_${getCurrentUserId()}`
export const getPrivateChatListTableName = () => `privateChatList_${getCurrentUserId()}`
export const getUserInfoTableName = () => `userInfo_${getCurrentUserId()}`

// 数据库实例
let db = null

/**
 * 简单的数据库打开函数
 */
export const openDatabase = () => {
  return new Promise((resolve, reject) => {
    if (db) {
      resolve(db)
      return
    }

    console.log('打开数据库，版本:', DB_VERSION)
    const request = indexedDB.open(DB_NAME, DB_VERSION)

    request.onerror = () => {
      console.error('数据库打开失败:', request.error)
      reject(request.error)
    }

    request.onsuccess = () => {
      db = request.result
      console.log('数据库打开成功')
      resolve(db)
    }

    request.onupgradeneeded = (event) => {
      db = event.target.result
      console.log('数据库升级中...')

      const tableName = getTableName()
      const chatListTableName = getChatListTableName()
      const privateChatListTableName = getPrivateChatListTableName()
      const userInfoTableName = getUserInfoTableName()

      // 创建消息表
      if (!db.objectStoreNames.contains(tableName)) {
        const store = db.createObjectStore(tableName, { keyPath: 'id' })

        // 创建索引
        store.createIndex('chatid', 'chatid', { unique: false })
        store.createIndex('fromid', 'fromid', { unique: false })
        store.createIndex('toid', 'toid', { unique: false })
        store.createIndex('t', 't', { unique: false })
        store.createIndex('timestamp', 'timestamp', { unique: false })
        store.createIndex('typecode', 'typecode', { unique: false })
        store.createIndex('typecode2', 'typecode2', { unique: false })
        store.createIndex('isRedRead', 'isRedRead', { unique: false })
        store.createIndex('idDel', 'idDel', { unique: false })

        console.log('消息表创建成功:', tableName)
      }

      // 创建群聊列表表
      if (!db.objectStoreNames.contains(chatListTableName)) {
        const chatListStore = db.createObjectStore(chatListTableName, { keyPath: 'id' })

        chatListStore.createIndex('chatId', 'chatId', { unique: false })
        chatListStore.createIndex('type', 'type', { unique: false })
        chatListStore.createIndex('timestamp', 'timestamp', { unique: false })

        console.log('群聊列表表创建成功:', chatListTableName)
      }

      // 创建私聊列表表
      if (!db.objectStoreNames.contains(privateChatListTableName)) {
        const privateChatListStore = db.createObjectStore(privateChatListTableName, { keyPath: 'id' })

        privateChatListStore.createIndex('chatId', 'chatId', { unique: false })
        privateChatListStore.createIndex('type', 'type', { unique: false })
        privateChatListStore.createIndex('timestamp', 'timestamp', { unique: false })

        console.log('私聊列表表创建成功:', privateChatListTableName)
      }

      // 创建用户信息表
      if (!db.objectStoreNames.contains(userInfoTableName)) {
        const userStore = db.createObjectStore(userInfoTableName, { keyPath: 'id' })

        userStore.createIndex('nickname', 'nickname', { unique: false })
        userStore.createIndex('timestamp', 'timestamp', { unique: false })

        console.log('用户信息表创建成功:', userInfoTableName)
      }
    }
  })
}

/**
 * 关闭数据库
 */
export const closeDatabase = () => {
  if (db) {
    db.close()
    db = null
    console.log('数据库已关闭')
  }
}

/**
 * 检查数据库连接状态
 */
export const isDatabaseConnected = () => {
  return db !== null && db.readyState !== 'closed'
}

/**
 * 删除数据库（用于重置）
 */
export const deleteDatabase = () => {
  return new Promise((resolve, reject) => {
    closeDatabase()
    
    const deleteRequest = indexedDB.deleteDatabase(DB_NAME)
    
    deleteRequest.onsuccess = () => {
      console.log('数据库已删除')
      resolve(true)
    }
    
    deleteRequest.onerror = () => {
      console.error('删除数据库失败:', deleteRequest.error)
      reject(deleteRequest.error)
    }
  })
}

/**
 * 安全初始化数据库
 */
export const initDatabaseSafe = async () => {
  try {
    console.log('开始安全初始化数据库...')
    
    // 先尝试正常打开
    try {
      await openDatabase()
      console.log('✅ 数据库初始化成功')
      return true
    } catch (error) {
      console.warn('数据库打开失败，尝试重置:', error.message)
      
      // 如果失败，删除数据库重新创建
      try {
        await deleteDatabase()
        await openDatabase()
        console.log('✅ 数据库重置后初始化成功')
        return true
      } catch (resetError) {
        console.error('数据库重置失败:', resetError)
        return false
      }
    }
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return false
  }
}

/**
 * 添加消息（简化版）
 */
export const addMessage = (messageData) => {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('数据库未连接'))
      return
    }

    const transaction = db.transaction([getTableName()], 'readwrite')
    const store = transaction.objectStore(getTableName())

    const processedMessage = {
      id: String(messageData.id || Date.now()),
      chatid: Number(messageData.chatid),
      fromid: Number(messageData.fromid),
      toid: Number(messageData.toid),
      msg: String(messageData.msg || ''),
      typecode: Number(messageData.typecode || 1),
      typecode2: Number(messageData.typecode2 || 0),
      t: String(messageData.t || new Date().toISOString()),
      timestamp: Number(messageData.timestamp || Date.now()),
      isRedRead: Number(messageData.isRedRead || 0),
      idDel: Number(messageData.idDel || 0),
      nickname: String(messageData.nickname || ''),
      avatar: String(messageData.avatar || '')
    }

    const request = store.put(processedMessage)

    request.onsuccess = () => {
      console.log('消息添加成功:', processedMessage.id)
      resolve(processedMessage)
    }

    request.onerror = () => {
      console.error('消息添加失败:', request.error)
      reject(request.error)
    }
  })
}

/**
 * 获取消息列表（简化版）
 */
export const getMessages = (chatId, limit = 20) => {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('数据库未连接'))
      return
    }

    const transaction = db.transaction([getTableName()], 'readonly')
    const store = transaction.objectStore(getTableName())
    const index = store.index('chatid')

    const request = index.getAll(Number(chatId))

    request.onsuccess = () => {
      const messages = request.result || []
      
      // 按时间排序
      messages.sort((a, b) => {
        const timeA = a.timestamp || new Date(a.t).getTime()
        const timeB = b.timestamp || new Date(b.t).getTime()
        return timeA - timeB
      })

      // 限制数量
      const limitedMessages = messages.slice(-limit)
      
      console.log(`获取消息: chatId=${chatId}, 数量=${limitedMessages.length}`)
      resolve(limitedMessages)
    }

    request.onerror = () => {
      console.error('获取消息失败:', request.error)
      reject(request.error)
    }
  })
}

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.dbSimple = {
    openDatabase,
    closeDatabase,
    deleteDatabase,
    initDatabaseSafe,
    addMessage,
    getMessages,
    isDatabaseConnected
  }
  
  console.log('🔧 简化数据库工具已加载: window.dbSimple')
}
