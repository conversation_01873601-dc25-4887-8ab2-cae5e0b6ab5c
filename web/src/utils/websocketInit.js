/**
 * WebSocket 初始化工具
 * 用于在应用启动时检查登录状态并自动建立WebSocket连接
 */

import { useUserStore } from '@/pinia/modules/user'
import { useWebSocketStore } from '@/pinia/modules/websocket'

/**
 * 初始化WebSocket连接
 * 检查用户登录状态，如果已登录则自动建立持久连接
 */
export const initWebSocketConnection = async (retryCount = 0) => {
  const maxRetries = 3
  const retryDelay = 1000 // 1秒

  try {
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()

    // 等待一小段时间确保 store 完全初始化
    if (retryCount === 0) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    console.log(`WebSocket初始化尝试 ${retryCount + 1}/${maxRetries + 1}`)

    // 检查用户是否已登录
    if (userStore.token) {
      console.log('检测到用户已登录，初始化WebSocket持久连接')
      console.log('用户Token:', userStore.token ? '已获取' : '未获取')

      // 启用持久连接
      webSocketStore.enablePersistentConnection()

      // 尝试建立连接
      try {
        const connected = await webSocketStore.initConnection(userStore.token, true)
        if (connected) {
          console.log('WebSocket持久连接初始化成功')
        } else {
          console.log('WebSocket初始连接失败，但持久连接机制已启用')
        }
      } catch (error) {
        console.log('WebSocket初始连接失败，持久连接机制将在后台重试:', error.message)
      }
    } else {
      console.log('用户未登录，跳过WebSocket连接初始化')

      // 如果是页面刷新后的初始化，可能 token 还在加载中，尝试重试
      if (retryCount < maxRetries) {
        console.log(`Token未加载，${retryDelay}ms后重试...`)
        setTimeout(() => {
          initWebSocketConnection(retryCount + 1)
        }, retryDelay)
      }
    }
  } catch (error) {
    console.error('WebSocket连接初始化过程出错:', error)

    // 如果出错且还有重试次数，则重试
    if (retryCount < maxRetries) {
      console.log(`初始化出错，${retryDelay}ms后重试...`)
      setTimeout(() => {
        initWebSocketConnection(retryCount + 1)
      }, retryDelay)
    }
  }
}

/**
 * 监听用户登录状态变化
 * 当用户登录时自动建立WebSocket连接，登出时关闭连接
 */
export const watchUserLoginStatus = () => {
  // 这个函数可以在需要时扩展，目前登录/登出逻辑已在user store中处理
  console.log('WebSocket用户状态监听器已设置')
}

/**
 * 获取WebSocket连接状态信息
 */
export const getWebSocketStatus = () => {
  try {
    const webSocketStore = useWebSocketStore()
    return {
      isConnected: webSocketStore.isConnected,
      isConnecting: webSocketStore.isConnecting,
      connectionStatus: webSocketStore.connectionStatus,
      autoReconnectEnabled: webSocketStore.autoReconnectEnabled,
      unreadCount: webSocketStore.unreadCount
    }
  } catch (error) {
    console.error('获取WebSocket状态失败:', error)
    return {
      isConnected: false,
      isConnecting: false,
      connectionStatus: 'disconnected',
      autoReconnectEnabled: false,
      unreadCount: 0
    }
  }
}

/**
 * 手动重连WebSocket
 */
export const reconnectWebSocket = async () => {
  try {
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()

    if (!userStore.token) {
      throw new Error('用户未登录，无法重连WebSocket')
    }

    console.log('手动重连WebSocket')
    await webSocketStore.forceReconnect(userStore.token)
    console.log('WebSocket重连成功')
    return true
  } catch (error) {
    console.error('WebSocket重连失败:', error)
    throw error
  }
}

/**
 * 页面加载完成后的WebSocket检查
 * 确保页面完全加载后再次检查WebSocket连接
 */
export const checkWebSocketAfterPageLoad = async () => {
  // 等待页面完全加载
  if (document.readyState !== 'complete') {
    await new Promise(resolve => {
      if (document.readyState === 'complete') {
        resolve()
      } else {
        window.addEventListener('load', resolve, { once: true })
      }
    })
  }

  // 页面加载完成后再次检查
  console.log('页面加载完成，检查WebSocket连接状态')

  try {
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()

    if (userStore.token && !webSocketStore.isConnected) {
      console.log('页面刷新后检测到用户已登录但WebSocket未连接，尝试重新连接')

      // 启用持久连接
      webSocketStore.enablePersistentConnection()

      // 尝试连接
      try {
        await webSocketStore.initConnection(userStore.token, true)
        console.log('页面刷新后WebSocket重连成功')
      } catch (error) {
        console.log('页面刷新后WebSocket重连失败，持久连接机制将继续尝试:', error.message)
      }
    }
  } catch (error) {
    console.error('页面加载后WebSocket检查失败:', error)
  }
}

/**
 * 应用启动时的WebSocket初始化
 * 应该在应用的main.js或app初始化时调用
 */
export const setupWebSocketForApp = async () => {
  console.log('开始设置应用级WebSocket连接')

  // 初始化连接
  await initWebSocketConnection()

  // 设置状态监听
  watchUserLoginStatus()

  // 设置页面加载完成后的检查
  setTimeout(() => {
    checkWebSocketAfterPageLoad()
  }, 2000) // 2秒后检查

  console.log('应用级WebSocket设置完成')
}
