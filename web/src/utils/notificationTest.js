/**
 * 消息通知功能测试文件
 * 用于测试消息通知和未读数量显示逻辑
 */

import { setChatDialogState } from './connectionService.js'
import { useWebSocketStore } from '@/pinia/modules/websocket.js'
import { useUserStore } from '@/pinia/modules/user.js'

/**
 * 测试消息通知功能
 */
export const testNotificationFeatures = () => {
  console.log('=== 开始测试消息通知功能 ===')
  
  // 测试聊天界面状态管理
  console.log('1. 测试聊天界面状态管理')
  setChatDialogState(false)
  console.log('聊天界面状态设置为关闭')
  
  setChatDialogState(true)
  console.log('聊天界面状态设置为打开')
  
  // 测试未读数量管理
  console.log('2. 测试未读数量管理')
  const webSocketStore = useWebSocketStore()
  
  console.log('当前未读数量:', webSocketStore.unreadCount)
  
  // 模拟增加未读数量
  webSocketStore.unreadCount = 5
  console.log('模拟设置未读数量为 5:', webSocketStore.unreadCount)
  
  // 测试清除未读数量
  webSocketStore.clearUnreadCount()
  console.log('清除未读数量后:', webSocketStore.unreadCount)
  
  console.log('=== 消息通知功能测试完成 ===')
}

/**
 * 模拟接收消息测试
 */
export const simulateMessageReceived = () => {
  console.log('=== 模拟接收消息测试 ===')
  
  const userStore = useUserStore()
  const currentUserId = userStore.userInfo?.id || userStore.userInfo?.userId
  
  // 模拟别人发送的私聊消息
  const mockPrivateMessage = {
    id: Date.now(),
    typecode: 1,
    typecode2: 0,
    fromid: '12345', // 不同于当前用户ID
    toid: currentUserId,
    msg: 'Hello, this is a test message!',
    t: new Date().toISOString(),
    groupID: null
  }
  
  console.log('模拟私聊消息:', mockPrivateMessage)
  console.log('当前用户ID:', currentUserId)
  console.log('是否为自己的消息:', mockPrivateMessage.fromid == currentUserId)
  
  // 模拟别人发送的群聊消息
  const mockGroupMessage = {
    id: Date.now() + 1,
    typecode: 2,
    typecode2: 0,
    fromid: '67890', // 不同于当前用户ID
    toid: 'group123',
    msg: 'Hello group, this is a test message!',
    t: new Date().toISOString(),
    groupID: 'group123'
  }
  
  console.log('模拟群聊消息:', mockGroupMessage)
  console.log('是否为自己的消息:', mockGroupMessage.fromid == currentUserId)
  
  console.log('=== 模拟接收消息测试完成 ===')
}

/**
 * 测试聊天界面打开/关闭对通知的影响
 */
export const testChatDialogStateEffect = () => {
  console.log('=== 测试聊天界面状态对通知的影响 ===')
  
  // 测试关闭状态
  setChatDialogState(false)
  console.log('聊天界面关闭状态 - 应该显示通知和增加未读数量')
  
  // 测试打开状态
  setChatDialogState(true)
  console.log('聊天界面打开状态 - 不应该显示通知和增加未读数量')
  
  console.log('=== 聊天界面状态测试完成 ===')
}

/**
 * 使用说明
 */
export const showUsageInstructions = () => {
  console.log(`
=== 消息通知功能使用说明 ===

1. 通知显示机制：
   - 只有别人发送的消息才显示通知
   - 聊天界面打开时不显示通知
   - 聊天界面关闭时才显示通知

2. 未读消息数量显示：
   - 收到别人的消息且聊天框关闭时增加未读数量
   - 未读数量显示在聊天弹窗图标上
   - 不显示在WebSocket连接状态图标上

3. 测试方法：
   - 在控制台运行: testNotificationFeatures()
   - 模拟消息: simulateMessageReceived()
   - 测试状态影响: testChatDialogStateEffect()

4. 主要功能：
   - 自动识别消息发送者（自己 vs 别人）
   - 根据聊天界面状态决定是否显示通知
   - 智能管理未读消息数量
   - 点击通知可打开聊天弹窗
`)
}
