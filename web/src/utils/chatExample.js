/**
 * 聊天功能使用示例
 * 展示如何使用 WebSocket 消息处理、数据库存储和消息发送功能
 */

import { useWebSocketStore } from '@/pinia/modules/websocket.js'
import { sendMessage } from './chatService.js'
import { getChatList, getChatMessages, markMessagesAsRead } from './db.js'

/**
 * 初始化聊天功能
 * @param {string} token - 用户token
 */
export const initChat = async (token) => {
  try {
    const webSocketStore = useWebSocketStore()
    
    // 初始化 WebSocket 连接
    await webSocketStore.initConnection(token, true)
    
    console.log('聊天功能初始化成功')
    return true
  } catch (error) {
    console.error('聊天功能初始化失败:', error)
    return false
  }
}

/**
 * 发送消息示例
 * @param {Object} messageData - 消息数据
 */
export const sendChatMessage = async (messageData) => {
  try {
    const {
      fromid,      // 发送者ID
      toId,        // 接收者ID
      msg,         // 消息内容
      typecode,    // 消息类型：1-私聊，2-群聊，3-通知
      typecode2,   // 内容类型：0-文本，1-音频，2-图片，3-视频
      groupID      // 群组ID（群聊时需要）
    } = messageData

    // 构建消息参数
    const messageParams = {
      fromid: Number(fromid),
      toId: Number(toId),
      msg: String(msg),
      typecode: Number(typecode),
      typecode2: Number(typecode2 || 0),
      t: new Date().toISOString()
    }

    // 如果是群聊消息，添加群组ID
    if (typecode === 2 && groupID) {
      messageParams.groupID = Number(groupID)
    }

    // 发送消息
    const result = await sendMessage(messageParams)
    console.log('消息发送成功:', result)
    
    return result
  } catch (error) {
    console.error('发送消息失败:', error)
    throw error
  }
}

/**
 * 获取聊天列表示例
 */
export const loadChatListExample = async () => {
  try {
    const webSocketStore = useWebSocketStore()
    const chatList = await webSocketStore.loadChatList()
    
    console.log('聊天列表:', chatList)
    return chatList
  } catch (error) {
    console.error('获取聊天列表失败:', error)
    return []
  }
}

/**
 * 获取聊天消息示例
 * @param {string} chatId - 聊天对象ID
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 */
export const loadChatMessagesExample = async (chatId, page = 1, size = 20) => {
  try {
    const webSocketStore = useWebSocketStore()
    const messages = await webSocketStore.loadChatMessages(chatId, page, size)
    
    console.log(`聊天消息 (chatId: ${chatId}):`, messages)
    return messages
  } catch (error) {
    console.error('获取聊天消息失败:', error)
    return []
  }
}

/**
 * 标记消息为已读示例
 * @param {string} chatId - 聊天对象ID
 */
export const markChatAsReadExample = async (chatId) => {
  try {
    const webSocketStore = useWebSocketStore()
    const success = await webSocketStore.markChatAsRead(chatId)
    
    if (success) {
      console.log(`聊天已标记为已读: ${chatId}`)
    }
    
    return success
  } catch (error) {
    console.error('标记消息为已读失败:', error)
    return false
  }
}

/**
 * 完整的聊天流程示例
 */
export const chatFlowExample = async () => {
  try {
    // 1. 初始化聊天功能
    const token = 'your-user-token'
    await initChat(token)

    // 2. 获取聊天列表
    const chatList = await loadChatListExample()
    
    if (chatList.length > 0) {
      const firstChat = chatList[0]
      console.log('选择第一个聊天:', firstChat)

      // 3. 获取聊天消息
      const messages = await loadChatMessagesExample(firstChat.chatId)
      console.log('聊天消息:', messages)

      // 4. 发送一条消息
      const messageData = {
        fromid: 10003,  // 当前用户ID
        toId: firstChat.chatId,
        msg: '你好，这是一条测试消息',
        typecode: firstChat.type === 'group' ? 2 : 1,  // 根据聊天类型设置
        typecode2: 0,   // 文本消息
        groupID: firstChat.type === 'group' ? firstChat.chatId : undefined
      }

      await sendChatMessage(messageData)

      // 5. 标记消息为已读
      await markChatAsReadExample(firstChat.chatId)
    }

    console.log('聊天流程示例完成')
  } catch (error) {
    console.error('聊天流程示例失败:', error)
  }
}

/**
 * 监听 WebSocket 消息示例
 */
export const setupMessageListenerExample = () => {
  const webSocketStore = useWebSocketStore()
  
  // 监听连接状态变化
  console.log('WebSocket 连接状态:', webSocketStore.connectionStatus)
  console.log('是否已连接:', webSocketStore.isConnected)
  
  // 监听最新消息
  console.log('最新消息:', webSocketStore.lastMessage)
  console.log('未读消息数:', webSocketStore.unreadCount)
  
  // 监听消息历史
  console.log('消息历史:', webSocketStore.messageHistory)
}

/**
 * 使用说明和示例
 */
export const usageInstructions = () => {
  console.log(`
=== 聊天功能使用说明 ===

1. 初始化聊天功能：
   import { initChat } from '@/utils/chatExample.js'
   await initChat('your-token')

2. 发送消息：
   import { sendChatMessage } from '@/utils/chatExample.js'
   await sendChatMessage({
     fromid: 10003,
     toId: 10001,
     msg: '你好',
     typecode: 1,  // 1-私聊, 2-群聊
     typecode2: 0  // 0-文本, 1-音频, 2-图片, 3-视频
   })

3. 获取聊天列表：
   import { useWebSocketStore } from '@/pinia/modules/websocket.js'
   const webSocketStore = useWebSocketStore()
   const chatList = await webSocketStore.loadChatList()

4. 获取聊天消息：
   const messages = await webSocketStore.loadChatMessages('chatId', 1, 20)

5. 标记消息为已读：
   await webSocketStore.markChatAsRead('chatId')

6. 监听 WebSocket 状态：
   console.log('连接状态:', webSocketStore.connectionStatus)
   console.log('未读消息数:', webSocketStore.unreadCount)
   console.log('最新消息:', webSocketStore.lastMessage)

=== 数据库字段说明 ===

消息对象字段：
- id: 消息ID
- typecode: 消息类型 (1-私聊, 2-群聊, 3-通知)
- typecode2: 内容类型 (0-文本, 1-音频, 2-图片, 3-视频, 9-语音通话)
- toid: 接收者ID
- fromid: 发送者ID
- chatid: 聊天对象ID
- t: 时间戳
- msg: 消息内容
- isRedRead: 是否已读 (0-未读, 1-已读)
- idDel: 是否删除 (0-未删除, 1-已删除)
- senderAvatar: 发送者头像
- senderNickname: 发送者昵称
- avatar: 头像
- nickname: 昵称
- lastMessage: 最后一条消息
- timestamp: 时间戳
- unreadCount: 未读消息数

=== 注意事项 ===

1. 确保在使用前先初始化 WebSocket 连接
2. 消息内容会自动加密/解密
3. 数据库使用 IndexedDB，数据存储在浏览器本地
4. 支持自动重连和持久连接
5. 消息接收会自动存储到本地数据库
6. 支持分页加载聊天消息
  `)
}
