/**
 * 应用初始化管理器
 * 统一管理数据库和WebSocket的初始化，处理依赖关系和错误重试
 */

/**
 * 初始化管理器类
 */
export class InitManager {
  constructor() {
    this.isInitialized = false
    this.initPromise = null
    this.retryCount = 0
    this.maxRetries = 3
  }

  /**
   * 等待Pinia初始化完成
   */
  async waitForPinia() {
    let attempts = 0
    const maxAttempts = 10
    
    while (attempts < maxAttempts) {
      try {
        // 尝试获取store
        const { useUserStore } = await import('@/pinia/modules/user')
        const userStore = useUserStore()
        
        // 如果能成功获取store，说明Pinia已初始化
        console.log('Pinia初始化检查通过')
        return true
      } catch (error) {
        attempts++
        console.log(`Pinia初始化检查失败，重试 ${attempts}/${maxAttempts}:`, error.message)
        
        if (attempts >= maxAttempts) {
          throw new Error('Pinia初始化超时')
        }
        
        // 等待500ms后重试
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }
  }

  /**
   * 初始化数据库
   */
  async initDatabase() {
    try {
      console.log('开始初始化数据库...')
      const { initDatabase } = await import('@/utils/db.js')
      const success = await initDatabase()
      
      if (success) {
        console.log('✅ 数据库初始化成功')
        return true
      } else {
        throw new Error('数据库初始化失败')
      }
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化聊天管理器
   */
  async initChatManager() {
    try {
      console.log('开始初始化聊天管理器...')
      const { chatManager } = await import('@/utils/chatManager.js')
      const success = await chatManager.init()
      
      if (success) {
        console.log('✅ 聊天管理器初始化成功')
        return true
      } else {
        throw new Error('聊天管理器初始化失败')
      }
    } catch (error) {
      console.error('❌ 聊天管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化WebSocket（延迟初始化）
   */
  async initWebSocket() {
    try {
      console.log('开始初始化WebSocket...')
      
      // 等待额外的时间确保用户状态加载完成
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const { setupWebSocketForApp } = await import('@/utils/websocketInit.js')
      await setupWebSocketForApp()
      
      console.log('✅ WebSocket初始化完成')
      return true
    } catch (error) {
      console.error('❌ WebSocket初始化失败:', error)
      // WebSocket初始化失败不阻止应用启动
      return false
    }
  }

  /**
   * 执行完整初始化流程
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('应用已初始化，跳过重复初始化')
      return true
    }

    if (this.initPromise) {
      console.log('初始化正在进行中，等待完成...')
      return await this.initPromise
    }

    this.initPromise = this._doInitialize()
    return await this.initPromise
  }

  /**
   * 内部初始化方法
   */
  async _doInitialize() {
    try {
      console.log('🚀 开始应用初始化流程...')

      // 1. 等待Pinia初始化
      console.log('1️⃣ 等待Pinia初始化...')
      await this.waitForPinia()

      // 2. 初始化数据库
      console.log('2️⃣ 初始化数据库...')
      await this.initDatabase()

      // 3. 初始化聊天管理器
      console.log('3️⃣ 初始化聊天管理器...')
      await this.initChatManager()

      // 4. 延迟初始化WebSocket
      console.log('4️⃣ 延迟初始化WebSocket...')
      setTimeout(() => {
        this.initWebSocket()
      }, 2000)

      this.isInitialized = true
      console.log('🎉 应用初始化完成！')
      return true

    } catch (error) {
      console.error('💥 应用初始化失败:', error)
      
      this.retryCount++
      if (this.retryCount <= this.maxRetries) {
        console.log(`🔄 初始化失败，${2000}ms后进行第${this.retryCount}次重试...`)
        
        // 重置Promise以允许重试
        this.initPromise = null
        
        setTimeout(() => {
          this.initialize()
        }, 2000)
        
        return false
      } else {
        console.error('❌ 初始化重试次数已达上限，应用可能无法正常工作')
        return false
      }
    }
  }

  /**
   * 手动重试初始化
   */
  async retry() {
    console.log('🔄 手动重试初始化...')
    this.isInitialized = false
    this.initPromise = null
    this.retryCount = 0
    return await this.initialize()
  }

  /**
   * 获取初始化状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      retryCount: this.retryCount,
      isInitializing: !!this.initPromise
    }
  }
}

// 创建单例实例
export const initManager = new InitManager()

/**
 * 应用启动时调用的初始化函数
 */
export const initializeApp = async () => {
  return await initManager.initialize()
}

/**
 * 手动重试初始化
 */
export const retryInitialization = async () => {
  return await initManager.retry()
}

/**
 * 获取初始化状态
 */
export const getInitStatus = () => {
  return initManager.getStatus()
}

/**
 * 在开发环境下暴露到全局，方便调试
 */
if (process.env.NODE_ENV === 'development') {
  window.initManager = initManager
  window.retryInit = retryInitialization
  window.getInitStatus = getInitStatus
}
