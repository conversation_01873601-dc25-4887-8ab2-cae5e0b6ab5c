/**
 * 群聊功能测试文件
 * 测试群聊列表获取、存储和消息管理功能
 */

/**
 * 测试群聊列表获取和存储
 */
export const testGroupListStorage = async () => {
  try {
    console.log('🧪 开始测试群聊列表获取和存储...')
    
    // 1. 测试聊天管理器初始化
    const { chatManager } = await import('./chatManager.js')
    await chatManager.init()
    console.log('✅ 聊天管理器初始化成功')
    
    // 2. 模拟群聊列表数据（模拟API返回的数据）
    const mockGroupList = [
      {
        id: 1001,
        groupName: '技术交流群',
        groupHeader: '/static/group1.jpg',
        createdAt: '2024-01-01T10:00:00Z',
        memberCount: 50
      },
      {
        id: 1002,
        groupName: '产品讨论群',
        groupHeader: '/static/group2.jpg',
        createdAt: '2024-01-02T10:00:00Z',
        memberCount: 30
      }
    ]
    
    // 3. 保存群聊列表到IndexedDB
    await chatManager.saveGroupList(mockGroupList)
    console.log('✅ 群聊列表已保存到IndexedDB')
    
    // 4. 从IndexedDB获取群聊列表
    const savedGroups = await chatManager.getGroupList()
    console.log('✅ 从IndexedDB获取群聊列表:', savedGroups)
    
    // 5. 验证数据完整性
    const isDataValid = savedGroups.length === mockGroupList.length &&
                       savedGroups.every(group => group.type === 'group')
    
    if (isDataValid) {
      console.log('✅ 群聊列表数据验证通过')
      return {
        success: true,
        groupCount: savedGroups.length,
        groups: savedGroups
      }
    } else {
      throw new Error('群聊列表数据验证失败')
    }
    
  } catch (error) {
    console.error('❌ 群聊列表测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试群聊消息存储和获取
 */
export const testGroupMessageStorage = async () => {
  try {
    console.log('🧪 开始测试群聊消息存储和获取...')
    
    const { chatManager } = await import('./chatManager.js')
    const currentUserId = 3001
    const groupId = 1001
    
    // 1. 发送测试群聊消息
    const testMessages = [
      {
        id: `msg_${Date.now()}_1`,
        fromid: currentUserId,
        toid: groupId,
        chatid: groupId,
        msg: '大家好，这是第一条测试消息！',
        typecode2: 0,
        nickname: '测试用户',
        avatar: '/static/test_user.jpg'
      },
      {
        id: `msg_${Date.now()}_2`,
        fromid: currentUserId,
        toid: groupId,
        chatid: groupId,
        msg: '这是第二条测试消息，测试时间排序',
        typecode2: 0,
        nickname: '测试用户',
        avatar: '/static/test_user.jpg'
      }
    ]
    
    // 发送消息
    for (const message of testMessages) {
      await chatManager.sendGroupMessage(message)
      // 稍微延迟确保时间戳不同
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    console.log('✅ 群聊消息发送成功')
    
    // 2. 获取群聊消息
    const messages = await chatManager.getGroupMessages(groupId, 1, 10)
    console.log('✅ 获取群聊消息:', messages)
    
    // 3. 验证消息顺序（应该按时间正序排列）
    const isOrderCorrect = messages.length >= 2 && 
                          messages[0].timestamp <= messages[1].timestamp
    
    if (isOrderCorrect) {
      console.log('✅ 消息时间排序验证通过')
    } else {
      console.warn('⚠️ 消息时间排序可能有问题')
    }
    
    return {
      success: true,
      messageCount: messages.length,
      messages: messages.slice(0, 3) // 只返回前3条用于展示
    }
    
  } catch (error) {
    console.error('❌ 群聊消息测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试群聊列表更新
 */
export const testGroupListUpdate = async () => {
  try {
    console.log('🧪 开始测试群聊列表更新...')
    
    const { chatManager } = await import('./chatManager.js')
    const groupId = 1001
    
    // 1. 获取当前群聊列表
    const groupsBefore = await chatManager.getGroupList()
    const targetGroup = groupsBefore.find(g => g.chatId === groupId)
    
    if (!targetGroup) {
      throw new Error('未找到目标群聊')
    }
    
    const oldLastMessage = targetGroup.lastMessage
    console.log('更新前的最后消息:', oldLastMessage)
    
    // 2. 模拟接收新消息
    const newMessage = {
      id: `msg_received_${Date.now()}`,
      fromid: 2001,
      toid: groupId,
      chatid: groupId,
      msg: '这是一条新接收的群聊消息',
      typecode: 2, // 群聊类型
      typecode2: 0,
      nickname: '其他用户',
      avatar: '/static/other_user.jpg',
      t: new Date().toISOString()
    }
    
    // 3. 使用聊天管理器接收消息
    await chatManager.receiveMessage(newMessage)
    console.log('✅ 新消息接收成功')
    
    // 4. 验证群聊列表是否更新
    const groupsAfter = await chatManager.getGroupList()
    const updatedGroup = groupsAfter.find(g => g.chatId === groupId)
    
    if (updatedGroup && updatedGroup.lastMessage !== oldLastMessage) {
      console.log('✅ 群聊列表更新成功')
      console.log('新的最后消息:', updatedGroup.lastMessage)
      return {
        success: true,
        oldLastMessage,
        newLastMessage: updatedGroup.lastMessage
      }
    } else {
      throw new Error('群聊列表未正确更新')
    }
    
  } catch (error) {
    console.error('❌ 群聊列表更新测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 运行所有群聊测试
 */
export const runAllGroupChatTests = async () => {
  console.log('🚀 开始运行群聊功能完整测试')
  console.log('=' .repeat(50))
  
  const results = {
    listStorage: await testGroupListStorage(),
    messageStorage: await testGroupMessageStorage(),
    listUpdate: await testGroupListUpdate()
  }
  
  const allPassed = Object.values(results).every(result => result.success)
  
  console.log('\n📊 群聊测试结果汇总:')
  console.log('群聊列表存储:', results.listStorage.success ? '✅' : '❌')
  console.log('群聊消息存储:', results.messageStorage.success ? '✅' : '❌')
  console.log('群聊列表更新:', results.listUpdate.success ? '✅' : '❌')
  
  if (allPassed) {
    console.log('🎉 所有群聊测试通过！')
  } else {
    console.log('⚠️ 部分群聊测试失败，请检查错误信息')
  }
  
  return {
    allPassed,
    results
  }
}

/**
 * 清理测试数据
 */
export const cleanupGroupChatTestData = async () => {
  try {
    console.log('🧹 开始清理群聊测试数据...')
    
    const { chatManager } = await import('./chatManager.js')
    
    // 清理群聊消息
    await chatManager.clearMessages(1001, true)
    await chatManager.clearMessages(1002, true)
    
    console.log('✅ 群聊测试数据清理完成')
    return true
  } catch (error) {
    console.error('❌ 清理群聊测试数据失败:', error)
    return false
  }
}

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.testGroupList = testGroupListStorage
  window.testGroupMessages = testGroupMessageStorage
  window.testGroupUpdate = testGroupListUpdate
  window.runGroupTests = runAllGroupChatTests
  window.cleanupGroupTests = cleanupGroupChatTestData
  
  console.log('🧪 群聊测试工具已加载:')
  console.log('  - window.testGroupList() - 测试群聊列表存储')
  console.log('  - window.testGroupMessages() - 测试群聊消息存储')
  console.log('  - window.testGroupUpdate() - 测试群聊列表更新')
  console.log('  - window.runGroupTests() - 运行所有群聊测试')
  console.log('  - window.cleanupGroupTests() - 清理测试数据')
}
