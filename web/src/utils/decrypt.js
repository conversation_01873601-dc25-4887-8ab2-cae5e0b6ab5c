// 消息解密
// 导入需要的加密库（uni-app支持crypto-js）
import CryptoJS from 'crypto-js'

/**
 * 解密AES加密后Base64编码的内容
 * @param {string} encryptedBase64 - 加密后的Base64字符串
 * @param {string} key - 密钥
 * @param {string} [iv] - 初始向量（CBC模式需要）
 * @returns {string} 解密后的原始字符串
 */
export const decryptAESBase64 = (encryptedBase64) => {
  try {
    const key = '0000000000000000'
    const iv = '0000000000000000'
    // 1. 将Base64字符串转换为CryptoJS支持的格式
    const encryptedData = CryptoJS.enc.Base64.parse(encryptedBase64)

    // 2. 将密钥和IV转换为CryptoJS支持的格式
    const cryptoKey = CryptoJS.enc.Utf8.parse(key)
    const cryptoIv = iv ? CryptoJS.enc.Utf8.parse(iv) : null

    // 3. 创建解密配置
    const cfg = {
      mode: CryptoJS.mode.CBC, // 根据加密时使用的模式调整
      padding: CryptoJS.pad.Pkcs7, // 根据加密时使用的填充调整
      iv: cryptoIv
    }

    // 4. 执行解密
    const decrypted = CryptoJS.AES.decrypt(
      { ciphertext: encryptedData },
      cryptoKey,
      cfg
    )

    // 5. 将解密结果转换为UTF-8字符串
    return decrypted.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('解密失败:', error)
    return null
  }
}

/**
 * AES加密内容并进行Base64编码
 * @param {string} plainText - 需要加密的原始字符串
 * @returns {string} Base64编码的加密字符串
 */
export const encryptAESBase64 = (plainText) => {
  try {
    const key = '0000000000000000'
    const iv = '0000000000000000'

    // 1. 将密钥和IV转换为CryptoJS支持的格式
    const cryptoKey = CryptoJS.enc.Utf8.parse(key)
    const cryptoIv = CryptoJS.enc.Utf8.parse(iv)

    // 2. 创建加密配置
    const cfg = {
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
      iv: cryptoIv
    }

    // 3. 执行加密
    const encrypted = CryptoJS.AES.encrypt(plainText, cryptoKey, cfg)

    // 4. 返回Base64编码的加密结果
    return encrypted.toString()
  } catch (error) {
    console.error('加密失败:', error)
    return null
  }
}

export const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 智能时间格式化函数
 * 当天的消息时间只显示时分秒
 * 隔天的消息显示天日分秒
 * 隔月的消息显示月日时分秒
 * 隔年的显示年月日时分秒
 */
export const formatSmartTime = (timestamp) => {
  const now = new Date()
  const date = new Date(timestamp)

  const nowYear = now.getFullYear()
  const nowMonth = now.getMonth()
  const nowDay = now.getDate()
  const nowDayOfWeek = now.getDay() // 0 (Sunday) to 6 (Saturday)

  const msgYear = date.getFullYear()
  const msgMonth = date.getMonth()
  const msgDay = date.getDate()
  const msgDayOfWeek = date.getDay()

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const dayOfMonth = String(date.getDate()).padStart(2, '0')
  const monthOfYear = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

  // 1. 当天消息：显示具体时间（如“10:30”）
  if (nowYear === msgYear && nowMonth === msgMonth && nowDay === msgDay) {
    return `${hours}:${minutes}`
  }

  // 计算日期差异（以天为单位）
  // 将时间设置为午夜以避免DST问题
  const nowDateOnly = new Date(nowYear, nowMonth, nowDay)
  const msgDateOnly = new Date(msgYear, msgMonth, msgDay)
  const diffTime = nowDateOnly.getTime() - msgDateOnly.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  // 2. 昨天：显示为 “昨天” + 时间（如“昨天 18:00”）
  if (diffDays === 1) {
    return `昨天 ${hours}:${minutes}`
  }

  // 3. 前天 ~ 6天内：显示 星期 + 时间（如“周二 14:20”）
  // (diffDays >= 2 && diffDays <= 6)
  // 另一种更可靠的判断方式是检查是否在同一周内，且不是今天或昨天
  // 为了简化，我们先用diffDays，但要注意跨周的情况
  // 如果消息日期在本周内（从周一到周日算一周，或者周日到周六）
  // 并且不是今天或昨天
  if (nowYear === msgYear) {
    const nowWeekStart = new Date(nowDateOnly)
    nowWeekStart.setDate(
      nowDateOnly.getDate() - (nowDayOfWeek === 0 ? 6 : nowDayOfWeek - 1)
    ) // 本周一
    if (msgDateOnly >= nowWeekStart && diffDays > 1 && diffDays <= 6) {
      return `${weekdays[msgDayOfWeek]} ${hours}:${minutes}`
    }
  }

  // 4. 7天前 ~ 当年内：显示 月-日 + 时间（如“3月15日 09:10”）
  if (nowYear === msgYear) {
    return `${monthOfYear}月${dayOfMonth}日 ${hours}:${minutes}`
  }

  // 5. 跨年消息：显示 完整日期 + 时间（如“2023年12月30日 10:00”）
  return `${year}年${monthOfYear}月${dayOfMonth}日 ${hours}:${minutes}`
}
