/**
 * 调试助手工具
 * 用于检查应用各个系统的初始化状态
 */

/**
 * 检查Pinia状态
 */
export const checkPiniaStatus = async () => {
  try {
    const { useUserStore } = await import('@/pinia/modules/user')
    const { useWebSocketStore } = await import('@/pinia/modules/websocket')
    
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()
    
    return {
      status: 'success',
      userStore: {
        hasToken: !!userStore.token,
        hasUserInfo: !!userStore.userInfo,
        formChatId: userStore.formChatId
      },
      webSocketStore: {
        isConnected: webSocketStore.isConnected,
        connectionStatus: webSocketStore.connectionStatus
      }
    }
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    }
  }
}

/**
 * 检查数据库状态
 */
export const checkDatabaseStatus = async () => {
  try {
    const { isDatabaseConnected, debugAllMessages } = await import('@/utils/db.js')
    
    const isConnected = isDatabaseConnected()
    let messageCount = 0
    
    if (isConnected) {
      try {
        const messages = await debugAllMessages()
        messageCount = messages.length
      } catch (error) {
        console.warn('获取消息数量失败:', error)
      }
    }
    
    return {
      status: 'success',
      isConnected,
      messageCount
    }
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    }
  }
}

/**
 * 检查聊天管理器状态
 */
export const checkChatManagerStatus = async () => {
  try {
    const { chatManager } = await import('@/utils/chatManager.js')
    
    return {
      status: 'success',
      isInitialized: chatManager.isInitialized
    }
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    }
  }
}

/**
 * 检查初始化管理器状态
 */
export const checkInitManagerStatus = async () => {
  try {
    const { getInitStatus } = await import('@/utils/initManager.js')
    
    return {
      status: 'success',
      ...getInitStatus()
    }
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    }
  }
}

/**
 * 获取完整的系统状态报告
 */
export const getSystemStatusReport = async () => {
  console.log('🔍 开始系统状态检查...')
  
  const report = {
    timestamp: new Date().toISOString(),
    pinia: await checkPiniaStatus(),
    database: await checkDatabaseStatus(),
    chatManager: await checkChatManagerStatus(),
    initManager: await checkInitManagerStatus()
  }
  
  console.log('📊 系统状态报告:', report)
  
  // 生成简化的状态摘要
  const summary = {
    pinia: report.pinia.status === 'success' ? '✅' : '❌',
    database: report.database.status === 'success' && report.database.isConnected ? '✅' : '❌',
    chatManager: report.chatManager.status === 'success' && report.chatManager.isInitialized ? '✅' : '❌',
    initManager: report.initManager.status === 'success' && report.initManager.isInitialized ? '✅' : '❌'
  }
  
  console.log('📋 状态摘要:', summary)
  
  return { report, summary }
}

/**
 * 尝试修复常见问题
 */
export const attemptSystemRepair = async () => {
  console.log('🔧 开始系统修复...')
  
  try {
    // 1. 重试初始化管理器
    const { retryInitialization } = await import('@/utils/initManager.js')
    const initSuccess = await retryInitialization()
    
    if (initSuccess) {
      console.log('✅ 初始化管理器修复成功')
      return true
    } else {
      console.log('❌ 初始化管理器修复失败')
      
      // 2. 尝试重新连接数据库
      try {
        const { reconnectDatabase } = await import('@/utils/db.js')
        const dbSuccess = await reconnectDatabase()
        
        if (dbSuccess) {
          console.log('✅ 数据库重连成功')
        } else {
          console.log('❌ 数据库重连失败')
        }
      } catch (error) {
        console.error('数据库重连异常:', error)
      }
      
      // 3. 尝试重新初始化聊天管理器
      try {
        const { chatManager } = await import('@/utils/chatManager.js')
        const chatSuccess = await chatManager.init()
        
        if (chatSuccess) {
          console.log('✅ 聊天管理器重新初始化成功')
        } else {
          console.log('❌ 聊天管理器重新初始化失败')
        }
      } catch (error) {
        console.error('聊天管理器重新初始化异常:', error)
      }
      
      return false
    }
  } catch (error) {
    console.error('系统修复异常:', error)
    return false
  }
}

/**
 * 清理系统数据（谨慎使用）
 */
export const cleanSystemData = async () => {
  const confirmed = confirm('⚠️ 这将清空所有聊天记录和缓存数据，确定要继续吗？')
  
  if (!confirmed) {
    console.log('用户取消了数据清理操作')
    return false
  }
  
  try {
    console.log('🧹 开始清理系统数据...')
    
    // 清理IndexedDB
    try {
      const { clearAllData } = await import('@/utils/db.js')
      await clearAllData()
      console.log('✅ IndexedDB数据已清理')
    } catch (error) {
      console.error('清理IndexedDB失败:', error)
    }
    
    // 清理localStorage
    try {
      const keysToKeep = ['token', 'userId'] // 保留重要的键
      const allKeys = Object.keys(localStorage)
      
      allKeys.forEach(key => {
        if (!keysToKeep.includes(key)) {
          localStorage.removeItem(key)
        }
      })
      
      console.log('✅ localStorage已清理')
    } catch (error) {
      console.error('清理localStorage失败:', error)
    }
    
    console.log('🎉 系统数据清理完成')
    return true
  } catch (error) {
    console.error('系统数据清理失败:', error)
    return false
  }
}

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.checkSystemStatus = getSystemStatusReport
  window.repairSystem = attemptSystemRepair
  window.cleanSystem = cleanSystemData
  window.checkPinia = checkPiniaStatus
  window.checkDB = checkDatabaseStatus
  window.checkChat = checkChatManagerStatus
  
  console.log('🛠️ 调试工具已加载:')
  console.log('  - window.checkSystemStatus() - 检查系统状态')
  console.log('  - window.repairSystem() - 尝试修复系统')
  console.log('  - window.cleanSystem() - 清理系统数据')
  console.log('  - window.checkPinia() - 检查Pinia状态')
  console.log('  - window.checkDB() - 检查数据库状态')
  console.log('  - window.checkChat() - 检查聊天管理器状态')
}
