import axios from 'axios'
import { useUserStore } from '@/pinia/modules/user'

let baseURL = 'http://43.198.105.182:82'
const BASEAPI = '/api/'

const service = axios.create({
  baseURL: `${baseURL}${BASEAPI}`,
  timeout: 999999
})
service.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    // 优先使用 localStorage 中的 setToken，如果没有则使用 userStore.token
    const setToken = localStorage.getItem('setToken')
    const token = setToken || userStore.token

    if (token) {
      config.headers = {
        'x-token': token,
        'Content-Type': 'application/json'
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    return Promise.reject(error)
  }
)

export default service