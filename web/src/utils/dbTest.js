/**
 * 数据库测试文件
 * 用于验证数据库初始化是否正常工作
 */

/**
 * 简单的数据库连接测试
 */
export const testDatabaseConnection = async () => {
  try {
    console.log('🧪 开始数据库连接测试...')
    
    // 动态导入数据库模块
    const { openDb, isDatabaseConnected } = await import('./db.js')
    
    // 尝试打开数据库
    const database = await openDb()
    console.log('✅ 数据库打开成功:', database)
    
    // 检查连接状态
    const isConnected = isDatabaseConnected()
    console.log('✅ 数据库连接状态:', isConnected)
    
    return {
      success: true,
      database,
      isConnected
    }
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试聊天管理器初始化
 */
export const testChatManagerInit = async () => {
  try {
    console.log('🧪 开始聊天管理器测试...')
    
    // 动态导入聊天管理器
    const { chatManager } = await import('./chatManager.js')
    
    // 尝试初始化
    const success = await chatManager.init()
    console.log('✅ 聊天管理器初始化结果:', success)
    
    return {
      success,
      isInitialized: chatManager.isInitialized
    }
  } catch (error) {
    console.error('❌ 聊天管理器测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试Pinia store访问
 */
export const testPiniaAccess = async () => {
  try {
    console.log('🧪 开始Pinia访问测试...')
    
    // 等待一段时间确保Pinia初始化
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 尝试访问store
    const { useUserStore } = await import('@/pinia/modules/user')
    const { useWebSocketStore } = await import('@/pinia/modules/websocket')
    
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()
    
    console.log('✅ Pinia store访问成功')
    console.log('用户store:', { hasToken: !!userStore.token })
    console.log('WebSocket store:', { isConnected: webSocketStore.isConnected })
    
    return {
      success: true,
      userStore: {
        hasToken: !!userStore.token,
        formChatId: userStore.formChatId
      },
      webSocketStore: {
        isConnected: webSocketStore.isConnected,
        connectionStatus: webSocketStore.connectionStatus
      }
    }
  } catch (error) {
    console.error('❌ Pinia访问测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 运行所有测试
 */
export const runAllTests = async () => {
  console.log('🚀 开始运行所有测试...')
  console.log('=' .repeat(50))
  
  const results = {
    database: await testDatabaseConnection(),
    chatManager: await testChatManagerInit(),
    pinia: await testPiniaAccess()
  }
  
  console.log('📊 测试结果汇总:')
  console.log('数据库:', results.database.success ? '✅' : '❌')
  console.log('聊天管理器:', results.chatManager.success ? '✅' : '❌')
  console.log('Pinia:', results.pinia.success ? '✅' : '❌')
  
  const allPassed = Object.values(results).every(result => result.success)
  
  if (allPassed) {
    console.log('🎉 所有测试通过！')
  } else {
    console.log('⚠️ 部分测试失败，请检查错误信息')
  }
  
  return {
    allPassed,
    results
  }
}

/**
 * 清理测试数据
 */
export const cleanupTestData = async () => {
  try {
    console.log('🧹 开始清理测试数据...')
    
    // 删除测试数据库
    await new Promise((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase('chatDb')
      deleteRequest.onsuccess = () => {
        console.log('✅ 测试数据库已删除')
        resolve()
      }
      deleteRequest.onerror = () => {
        console.error('❌ 删除测试数据库失败')
        reject(deleteRequest.error)
      }
    })
    
    return true
  } catch (error) {
    console.error('清理测试数据失败:', error)
    return false
  }
}

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.testDB = testDatabaseConnection
  window.testChat = testChatManagerInit
  window.testPinia = testPiniaAccess
  window.runTests = runAllTests
  window.cleanupTests = cleanupTestData
  
  console.log('🧪 测试工具已加载:')
  console.log('  - window.testDB() - 测试数据库连接')
  console.log('  - window.testChat() - 测试聊天管理器')
  console.log('  - window.testPinia() - 测试Pinia访问')
  console.log('  - window.runTests() - 运行所有测试')
  console.log('  - window.cleanupTests() - 清理测试数据')
}
