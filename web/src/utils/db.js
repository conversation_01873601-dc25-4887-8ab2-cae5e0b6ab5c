
// 数据库配置
export const DB_NAME = 'chatDb'
export const DB_VERSION = 2  // 升级版本以支持新的数据结构

// 获取当前用户ID
const getCurrentUserId = () => {
  return localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default'
}

// 获取表名
export const getTableName = () => `chatData_${getCurrentUserId()}`

// 获取用户信息表名
export const getUserInfoTableName = () => `userInfo_${getCurrentUserId()}`

// 数据库实例
let db = null

// 存储当前数据库版本，用于动态升级
let currentDbVersion = DB_VERSION

/**
 * 数据结构定义
 * {
 *   id: String,              // 消息ID
 *   typecode: Number,        // 消息类型
 *   typecode2: Number,       // 内容类型
 *   toid: Number,           // 接收者ID
 *   fromid: Number,         // 发送者ID
 *   chatid: Number,         // 聊天对象ID
 *   t: String,              // 时间戳
 *   msg: String,            // 解密后消息内容
 *   isRedRead: Number,      // 已读状态 (0-未读，1-已读)
 *   idDel: Number,          // 删除状态 (0-正常，1-已删除)
 *   avatar: String,         // 发送者头像URL
 *   nickname: String,       // 发送者昵称
 *   senderAvatar: String,   // 群聊中发送者头像
 *   senderNickname: String, // 群聊中发送者昵称
 *   lastMessage: String,    // 聊天列表显示文本
 *   timestamp: Number,      // 时间戳(数字格式)
 *   unreadCount: Number     // 未读消息数
 * }
 */

/**
 * 确保对象存储存在
 * @returns {Promise<IDBDatabase>}
 */
const ensureObjectStore = () => {
  return new Promise((resolve, reject) => {
    const tableName = getTableName()
    const chatListTableName = getChatListTableName()
    const privateChatListTableName = getPrivateChatListTableName()
    const userInfoTableName = getUserInfoTableName()

    // 检查所有必要的表是否存在
    const requiredTables = [tableName, chatListTableName, privateChatListTableName, userInfoTableName]
    const missingTables = requiredTables.filter(table => !db || !db.objectStoreNames.contains(table))

    // 如果数据库不存在或有缺失的表，需要升级
    if (!db || missingTables.length > 0) {
      if (db) {
        db.close()
        db = null
      }

      // 递增版本号
      currentDbVersion++
      const request = indexedDB.open(DB_NAME, currentDbVersion)

      request.onerror = () => {
        console.error('数据库升级失败:', request.error)
        reject(request.error)
      }

      request.onsuccess = () => {
        db = request.result
        console.log('数据库升级成功，版本:', currentDbVersion)
        resolve(db)
      }

      request.onupgradeneeded = (event) => {
        db = event.target.result
        console.log('数据库升级中，创建缺失的对象存储:', missingTables)

        // 创建消息表
        if (!db.objectStoreNames.contains(tableName)) {
          const store = db.createObjectStore(tableName, { keyPath: 'id' })

          // 创建完整的索引以匹配数据结构
          store.createIndex('chatid', 'chatid', { unique: false })
          store.createIndex('fromid', 'fromid', { unique: false })
          store.createIndex('toid', 'toid', { unique: false })
          store.createIndex('t', 't', { unique: false })
          store.createIndex('timestamp', 'timestamp', { unique: false })
          store.createIndex('typecode', 'typecode', { unique: false })
          store.createIndex('typecode2', 'typecode2', { unique: false })
          store.createIndex('isRedRead', 'isRedRead', { unique: false })
          store.createIndex('idDel', 'idDel', { unique: false })
          store.createIndex('nickname', 'nickname', { unique: false })
          store.createIndex('senderNickname', 'senderNickname', { unique: false })

          console.log('消息表创建成功:', tableName)
        }

        // 创建群聊列表表
        if (!db.objectStoreNames.contains(chatListTableName)) {
          const chatListStore = db.createObjectStore(chatListTableName, { keyPath: 'id' })

          chatListStore.createIndex('chatId', 'chatId', { unique: false })
          chatListStore.createIndex('type', 'type', { unique: false })
          chatListStore.createIndex('timestamp', 'timestamp', { unique: false })
          chatListStore.createIndex('typecode', 'typecode', { unique: false })

          console.log('群聊列表表创建成功:', chatListTableName)
        }

        // 创建私聊列表表
        if (!db.objectStoreNames.contains(privateChatListTableName)) {
          const privateChatListStore = db.createObjectStore(privateChatListTableName, { keyPath: 'id' })

          privateChatListStore.createIndex('chatId', 'chatId', { unique: false })
          privateChatListStore.createIndex('type', 'type', { unique: false })
          privateChatListStore.createIndex('timestamp', 'timestamp', { unique: false })
          privateChatListStore.createIndex('typecode', 'typecode', { unique: false })

          console.log('私聊列表表创建成功:', privateChatListTableName)
        }

        // 创建用户信息表
        if (!db.objectStoreNames.contains(userInfoTableName)) {
          const userStore = db.createObjectStore(userInfoTableName, { keyPath: 'id' })

          userStore.createIndex('nickname', 'nickname', { unique: false })
          userStore.createIndex('timestamp', 'timestamp', { unique: false })

          console.log('用户信息表创建成功:', userInfoTableName)
        }
      }
    } else {
      resolve(db)
    }
  })
}

/**
 * 打开数据库
 * @returns {Promise<IDBDatabase>}
 */
export const openDb = () => {
  return new Promise((resolve, reject) => {
    if (db) {
      resolve(db)
      return
    }

    const request = indexedDB.open(DB_NAME, currentDbVersion)

    request.onerror = () => {
      console.error('数据库打开失败:', request.error)
      reject(request.error)
    }

    request.onsuccess = () => {
      db = request.result
      console.log('数据库打开成功')
      resolve(db)
    }

    request.onupgradeneeded = (event) => {
      db = event.target.result
      console.log('数据库升级中...')

      const tableName = getTableName()
      const userInfoTableName = getUserInfoTableName()
      const chatListTableName = getChatListTableName()
      const privateChatListTableName = getPrivateChatListTableName()

      // 创建消息表
      if (!db.objectStoreNames.contains(tableName)) {
        const store = db.createObjectStore(tableName, { keyPath: 'id' })

        // 创建完整的索引以匹配数据结构
        store.createIndex('chatid', 'chatid', { unique: false })
        store.createIndex('fromid', 'fromid', { unique: false })
        store.createIndex('toid', 'toid', { unique: false })
        store.createIndex('t', 't', { unique: false })
        store.createIndex('timestamp', 'timestamp', { unique: false })
        store.createIndex('typecode', 'typecode', { unique: false })
        store.createIndex('typecode2', 'typecode2', { unique: false })
        store.createIndex('isRedRead', 'isRedRead', { unique: false })
        store.createIndex('idDel', 'idDel', { unique: false })
        store.createIndex('nickname', 'nickname', { unique: false })
        store.createIndex('senderNickname', 'senderNickname', { unique: false })

        console.log('消息表创建成功:', tableName)
      }

      // 创建用户信息缓存表
      if (!db.objectStoreNames.contains(userInfoTableName)) {
        const userStore = db.createObjectStore(userInfoTableName, { keyPath: 'id' })

        // 创建用户信息索引
        userStore.createIndex('nickname', 'nickname', { unique: false })
        userStore.createIndex('timestamp', 'timestamp', { unique: false })

        console.log('用户信息表创建成功:', userInfoTableName)
      }

      // 创建群聊列表表
      if (!db.objectStoreNames.contains(chatListTableName)) {
        const chatListStore = db.createObjectStore(chatListTableName, { keyPath: 'id' })

        // 创建聊天列表索引
        chatListStore.createIndex('chatId', 'chatId', { unique: false })
        chatListStore.createIndex('type', 'type', { unique: false })
        chatListStore.createIndex('timestamp', 'timestamp', { unique: false })
        chatListStore.createIndex('typecode', 'typecode', { unique: false })

        console.log('群聊列表表创建成功:', chatListTableName)
      }

      // 创建私聊列表表
      if (!db.objectStoreNames.contains(privateChatListTableName)) {
        const privateChatListStore = db.createObjectStore(privateChatListTableName, { keyPath: 'id' })

        // 创建私聊列表索引
        privateChatListStore.createIndex('chatId', 'chatId', { unique: false })
        privateChatListStore.createIndex('type', 'type', { unique: false })
        privateChatListStore.createIndex('timestamp', 'timestamp', { unique: false })
        privateChatListStore.createIndex('typecode', 'typecode', { unique: false })

        console.log('私聊列表表创建成功:', privateChatListTableName)
      }
    }
  })
}

/**
 * 关闭数据库
 */
export const closeDb = () => {
  if (db) {
    db.close()
    db = null
    console.log('数据库已关闭')
  }
}

/**
 * 创建表
 * @returns {Promise<boolean>}
 */
export const addTab = async () => {
  try {
    await openDb()
    return true
  } catch (error) {
    console.error('创建表失败:', error)
    return false
  }
}

/**
 * 添加消息到数据库
 * @param {Object} item - 消息对象
 * @returns {Promise<Object>}
 */
export const addTabItem = async (item) => {
  try {
    if (!item) {
      throw new Error('参数不能为空')
    }

    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())

      // 处理数据字段，完全匹配你的数据结构
      const processedItem = {
        id: String(item.id) || String(Date.now()),           // 消息ID (String)
        typecode: Number(item.typecode) || 0,                // 消息类型 (Number)
        typecode2: Number(item.typecode2) || 0,              // 内容类型 (Number)
        toid: Number(item.toid) || 0,                        // 接收者ID (Number)
        fromid: Number(item.fromid) || 0,                    // 发送者ID (Number)
        chatid: Number(item.chatid) || 0,                    // 聊天对象ID (Number)
        t: String(item.t || new Date().toISOString()),       // 时间戳 (String)
        msg: String(item.msg || ''),                         // 解密后消息内容 (String)
        isRedRead: Number(item.isRedRead) || 0,              // 已读状态 (Number: 0-未读，1-已读)
        idDel: Number(item.idDel) || 0,                      // 删除状态 (Number: 0-正常，1-已删除)
        avatar: String(item.avatar || ''),                   // 发送者头像URL (String)
        nickname: String(item.nickname || ''),               // 发送者昵称 (String)
        senderAvatar: String(item.senderAvatar || ''),       // 群聊中发送者头像 (String)
        senderNickname: String(item.senderNickname || ''),   // 群聊中发送者昵称 (String)
        lastMessage: String(item.lastMessage || item.msg || ''), // 聊天列表显示文本 (String)
        timestamp: Number(item.timestamp) || new Date().getTime(), // 时间戳(数字格式) (Number)
        unreadCount: Number(item.unreadCount) || 0           // 未读消息数 (Number)
      }

      // 验证必要字段
      if (!processedItem.toid || !processedItem.fromid || !processedItem.chatid) {
        reject(new Error('必要字段缺失：toid, fromid, chatid'))
        return
      }

      // 如果没有提供lastMessage，使用msg作为默认值
      if (!processedItem.lastMessage && processedItem.msg) {
        processedItem.lastMessage = processedItem.msg
      }

      const request = store.put(processedItem)

      request.onsuccess = () => {
        console.log('消息添加成功:', processedItem.id)
        resolve(processedItem)
      }

      request.onerror = () => {
        console.error('消息添加失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('添加消息处理错误:', error)
    throw error
  }
}

/**
 * 根据chatId获取聊天消息（区分群聊和私聊）
 * @param {string} chatId - 聊天对象ID
 * @param {boolean} isGroup - 是否为群聊
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 * @returns {Promise<Array>}
 */
export const getChatMessages = async (chatId, isGroup = false, page = 1, size = 20) => {
  try {
    if (!chatId) {
      throw new Error('chatId is required')
    }

    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const index = store.index('chatid')

      // 根据是否为群聊构造查询条件
      const chatIdNum = Number(chatId)
      const chatIdStr = String(chatId)

      console.log(`查询聊天消息: chatId=${chatId}, isGroup=${isGroup}, 数字=${chatIdNum}, 字符串=${chatIdStr}`)

      const request = index.getAll(chatIdNum)

      request.onsuccess = () => {
        let messages = request.result || []

        // 如果用数字类型查询没有结果，尝试用字符串类型查询
        if (messages.length === 0 && chatIdNum !== chatIdStr) {
          console.log(`数字类型查询无结果，尝试字符串类型查询: ${chatIdStr}`)
          const stringRequest = index.getAll(chatIdStr)

          stringRequest.onsuccess = () => {
            messages = stringRequest.result || []
            console.log(`字符串类型查询结果: ${messages.length} 条消息`)
            processAndResolve(messages)
          }

          stringRequest.onerror = () => {
            console.error('字符串类型查询失败:', stringRequest.error)
            processAndResolve([])
          }
        } else {
          processAndResolve(messages)
        }
      }

      function processAndResolve(messages) {
        // 过滤消息：根据typecode区分群聊(2)和私聊(1)
        const expectedTypecode = isGroup ? 2 : 1
        const filteredMessages = messages.filter(msg => {
          // 如果没有typecode字段，根据isGroup参数判断
          if (msg.typecode === undefined || msg.typecode === null) {
            return true // 保留所有消息，让调用方决定
          }
          return msg.typecode === expectedTypecode
        })

        // 过滤掉已删除的消息
        const validMessages = filteredMessages.filter(msg => msg.idDel !== 1)

        // 按时间戳正序排序（最早的消息在前，最新的消息在后）
        validMessages.sort((a, b) => {
          const timeA = a.timestamp || new Date(a.t).getTime()
          const timeB = b.timestamp || new Date(b.t).getTime()
          return timeA - timeB
        })

        // 分页处理 - 从最新的消息开始分页（倒序分页）
        const totalMessages = validMessages.length
        const startIndex = Math.max(0, totalMessages - page * size)
        const endIndex = totalMessages - (page - 1) * size
        const paginatedMessages = validMessages.slice(startIndex, endIndex)

        console.log(`获取聊天消息: chatId=${chatId}, isGroup=${isGroup}, 总数=${totalMessages}, 返回=${paginatedMessages.length}`)

        // 打印前几条消息的详细信息用于调试
        if (validMessages.length > 0) {
          console.log('最新3条消息详情:', validMessages.slice(-3).map(msg => ({
            id: msg.id,
            chatid: msg.chatid,
            chatidType: typeof msg.chatid,
            msg: msg.msg,
            timestamp: msg.timestamp,
            typecode: msg.typecode,
            t: msg.t
          })))
        }

        resolve(paginatedMessages)
      }

      request.onerror = () => {
        console.error('获取聊天消息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取聊天消息处理错误:', error)
    throw error
  }
}

/**
 * 保存聊天消息（区分群聊和私聊）
 * @param {Object} messageData - 消息数据
 * @param {boolean} isGroup - 是否为群聊
 * @returns {Promise<Object>}
 */
export const saveChatMessage = async (messageData, isGroup = false) => {
  try {
    // 设置正确的typecode
    const processedMessage = {
      ...messageData,
      typecode: isGroup ? 2 : 1, // 群聊为2，私聊为1
      timestamp: messageData.timestamp || Date.now(),
      t: messageData.t || new Date().toISOString()
    }

    // 保存消息到数据库
    const savedMessage = await addTabItem(processedMessage)

    // 更新对应的聊天列表
    if (savedMessage) {
      const lastMessage = processedMessage.msg || ''
      await updateChatListLastMessage(processedMessage.chatid, lastMessage, isGroup)
    }

    return savedMessage
  } catch (error) {
    console.error('保存聊天消息失败:', error)
    throw error
  }
}

/**
 * 聊天列表表名
 */
export const getChatListTableName = () => `chatList_${getCurrentUserId()}`

/**
 * 私聊列表表名
 */
export const getPrivateChatListTableName = () => `privateChatList_${getCurrentUserId()}`

/**
 * 获取聊天对象列表（从消息记录中提取）
 * @returns {Promise<Array>}
 */
export const getChatList = async () => {
  try {
    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const request = store.getAll()

      request.onsuccess = () => {
        const messages = request.result || []

        // 按chatid分组，获取每个聊天对象的最新消息
        const chatMap = new Map()

        messages.forEach(message => {
          const chatId = message.chatid
          const currentTime = message.timestamp || new Date(message.t).getTime()
          const existingMessage = chatMap.get(chatId)
          const existingTime = existingMessage ? (existingMessage.timestamp || new Date(existingMessage.t).getTime()) : 0

          if (!chatMap.has(chatId) || currentTime > existingTime) {
            chatMap.set(chatId, message)
          }
        })

        // 转换为数组并按时间排序
        const chatList = Array.from(chatMap.values())
          .sort((a, b) => new Date(b.t).getTime() - new Date(a.t).getTime())
          .map(message => ({
            chatId: message.chatid,
            name: message.nickname || message.senderNickname || `用户${message.chatid}`,
            avatar: message.avatar || message.senderAvatar || '/static/My/avatar.jpg',
            lastMessage: message.lastMessage || message.msg || '',
            lastTime: message.t,
            timestamp: message.timestamp,
            unreadCount: message.unreadCount || 0,
            type: message.typecode === 2 ? 'group' : 'contact'
          }))

        console.log('获取聊天列表成功:', chatList.length, '个')
        resolve(chatList)
      }

      request.onerror = () => {
        console.error('获取聊天列表失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取聊天列表处理错误:', error)
    throw error
  }
}

/**
 * 保存群聊列表到IndexedDB
 * @param {Array} groupList - 群聊列表
 * @returns {Promise<boolean>}
 */
export const saveGroupChatList = async (groupList) => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getChatListTableName()], 'readwrite')
      const store = transaction.objectStore(getChatListTableName())

      // 清空现有群聊数据
      const clearRequest = store.clear()

      clearRequest.onsuccess = () => {
        let savedCount = 0
        const totalCount = groupList.length

        if (totalCount === 0) {
          resolve(true)
          return
        }

        groupList.forEach(group => {
          const chatItem = {
            id: `group_${group.id}`, // 群聊ID前缀
            chatId: group.id,
            name: group.name || group.GroupName,
            avatar: group.avatar || group.GroupHeader || '/static/My/avatar.jpg',
            lastMessage: '',
            lastTime: new Date().toISOString(),
            timestamp: Date.now(),
            unreadCount: 0,
            type: 'group',
            typecode: 2, // 群聊类型
            isGroup: true
          }

          const saveRequest = store.put(chatItem)

          saveRequest.onsuccess = () => {
            savedCount++
            if (savedCount === totalCount) {
              console.log(`群聊列表保存成功: ${savedCount} 个群聊`)
              resolve(true)
            }
          }

          saveRequest.onerror = () => {
            console.error('保存群聊失败:', saveRequest.error)
            reject(saveRequest.error)
          }
        })
      }

      clearRequest.onerror = () => {
        console.error('清空群聊列表失败:', clearRequest.error)
        reject(clearRequest.error)
      }
    })
  } catch (error) {
    console.error('保存群聊列表失败:', error)
    throw error
  }
}

/**
 * 获取群聊列表
 * @returns {Promise<Array>}
 */
export const getGroupChatList = async () => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getChatListTableName()], 'readonly')
      const store = transaction.objectStore(getChatListTableName())
      const request = store.getAll()

      request.onsuccess = () => {
        const chatList = request.result || []
        const groupChats = chatList
          .filter(chat => chat.type === 'group')
          .sort((a, b) => b.timestamp - a.timestamp)

        console.log('获取群聊列表成功:', groupChats.length, '个群聊')
        resolve(groupChats)
      }

      request.onerror = () => {
        console.error('获取群聊列表失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取群聊列表失败:', error)
    throw error
  }
}

/**
 * 添加私聊到列表（点击用户头像时调用）
 * @param {Object} userInfo - 用户信息
 * @returns {Promise<Object>}
 */
export const addPrivateChat = async (userInfo) => {
  try {
    await ensureObjectStore()

    const chatItem = {
      id: `private_${userInfo.id}`, // 私聊ID前缀
      chatId: userInfo.id, // 使用用户ID作为chatId
      name: userInfo.name || userInfo.nickname || `用户${userInfo.id}`,
      avatar: userInfo.avatar || userInfo.head_img || '/static/My/avatar.jpg',
      lastMessage: '',
      lastTime: new Date().toISOString(),
      timestamp: Date.now(),
      unreadCount: 0,
      type: 'private',
      typecode: 1, // 私聊类型
      isGroup: false
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getPrivateChatListTableName()], 'readwrite')
      const store = transaction.objectStore(getPrivateChatListTableName())

      const request = store.put(chatItem)

      request.onsuccess = () => {
        console.log('私聊添加成功:', userInfo.id)
        resolve(chatItem)
      }

      request.onerror = () => {
        console.error('私聊添加失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('添加私聊失败:', error)
    throw error
  }
}

/**
 * 获取私聊列表
 * @returns {Promise<Array>}
 */
export const getPrivateChatList = async () => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getPrivateChatListTableName()], 'readonly')
      const store = transaction.objectStore(getPrivateChatListTableName())
      const request = store.getAll()

      request.onsuccess = () => {
        const privateChats = (request.result || [])
          .sort((a, b) => b.timestamp - a.timestamp)

        console.log('获取私聊列表成功:', privateChats.length, '个私聊')
        resolve(privateChats)
      }

      request.onerror = () => {
        console.error('获取私聊列表失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取私聊列表失败:', error)
    throw error
  }
}

/**
 * 更新聊天列表的最后消息
 * @param {string} chatId - 聊天ID
 * @param {string} lastMessage - 最后消息
 * @param {boolean} isGroup - 是否为群聊
 * @returns {Promise<boolean>}
 */
export const updateChatListLastMessage = async (chatId, lastMessage, isGroup = false) => {
  try {
    await ensureObjectStore()

    const tableName = isGroup ? getChatListTableName() : getPrivateChatListTableName()
    const itemId = isGroup ? `group_${chatId}` : `private_${chatId}`

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)

      // 先获取现有记录
      const getRequest = store.get(itemId)

      getRequest.onsuccess = () => {
        const existingItem = getRequest.result

        if (existingItem) {
          // 更新现有记录
          existingItem.lastMessage = lastMessage
          existingItem.lastTime = new Date().toISOString()
          existingItem.timestamp = Date.now()

          const updateRequest = store.put(existingItem)

          updateRequest.onsuccess = () => {
            console.log(`聊天列表更新成功: ${chatId}`)
            resolve(true)
          }

          updateRequest.onerror = () => {
            console.error('更新聊天列表失败:', updateRequest.error)
            reject(updateRequest.error)
          }
        } else {
          console.log(`聊天记录不存在: ${chatId}`)
          resolve(false)
        }
      }

      getRequest.onerror = () => {
        console.error('获取聊天记录失败:', getRequest.error)
        reject(getRequest.error)
      }
    })
  } catch (error) {
    console.error('更新聊天列表失败:', error)
    throw error
  }
}

/**
 * 获取合并的聊天列表（群聊+私聊）
 * @returns {Promise<Array>}
 */
export const getAllChatList = async () => {
  try {
    const [groupChats, privateChats] = await Promise.all([
      getGroupChatList(),
      getPrivateChatList()
    ])

    // 合并并按时间排序
    const allChats = [...groupChats, ...privateChats]
      .sort((a, b) => b.timestamp - a.timestamp)

    console.log(`获取合并聊天列表: 群聊${groupChats.length}个, 私聊${privateChats.length}个, 总计${allChats.length}个`)
    return allChats
  } catch (error) {
    console.error('获取合并聊天列表失败:', error)
    throw error
  }
}

/**
 * 清空聊天记录（保留聊天列表）
 * @param {string} chatId - 聊天ID
 * @param {boolean} isGroup - 是否为群聊
 * @returns {Promise<boolean>}
 */
export const clearChatMessages = async (chatId, isGroup = false) => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())
      const index = store.index('chatid')

      const chatIdNum = Number(chatId)
      const request = index.getAll(chatIdNum)

      request.onsuccess = () => {
        const messages = request.result || []
        const expectedTypecode = isGroup ? 2 : 1

        // 过滤出对应类型的消息
        const targetMessages = messages.filter(msg =>
          msg.typecode === expectedTypecode || msg.typecode === undefined
        )

        if (targetMessages.length === 0) {
          resolve(true)
          return
        }

        let deletedCount = 0
        targetMessages.forEach(message => {
          const deleteRequest = store.delete(message.id)

          deleteRequest.onsuccess = () => {
            deletedCount++
            if (deletedCount === targetMessages.length) {
              console.log(`清空聊天记录成功: chatId=${chatId}, isGroup=${isGroup}, 删除${deletedCount}条`)
              resolve(true)
            }
          }

          deleteRequest.onerror = () => {
            console.error('删除消息失败:', deleteRequest.error)
            reject(deleteRequest.error)
          }
        })
      }

      request.onerror = () => {
        console.error('获取聊天消息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('清空聊天记录失败:', error)
    throw error
  }
}

/**
 * 删除聊天列表项
 * @param {string} chatId - 聊天ID
 * @param {boolean} isGroup - 是否为群聊
 * @returns {Promise<boolean>}
 */
export const deleteChatListItem = async (chatId, isGroup = false) => {
  try {
    await ensureObjectStore()

    const tableName = isGroup ? getChatListTableName() : getPrivateChatListTableName()
    const itemId = isGroup ? `group_${chatId}` : `private_${chatId}`

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)

      const deleteRequest = store.delete(itemId)

      deleteRequest.onsuccess = () => {
        console.log(`聊天列表项删除成功: ${chatId}`)
        resolve(true)
      }

      deleteRequest.onerror = () => {
        console.error('删除聊天列表项失败:', deleteRequest.error)
        reject(deleteRequest.error)
      }
    })
  } catch (error) {
    console.error('删除聊天列表项失败:', error)
    throw error
  }
}

/**
 * 搜索聊天记录
 * @param {string} keyword - 搜索关键词
 * @param {string} chatId - 聊天ID（可选）
 * @param {boolean} isGroup - 是否为群聊（可选）
 * @returns {Promise<Array>}
 */
export const searchChatMessages = async (keyword, chatId = null, isGroup = null) => {
  try {
    if (!keyword || keyword.trim() === '') {
      return []
    }

    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const request = store.getAll()

      request.onsuccess = () => {
        const messages = request.result || []
        const searchTerm = keyword.trim().toLowerCase()

        let filteredMessages = messages.filter(msg => {
          // 过滤已删除的消息
          if (msg.idDel === 1) return false

          // 如果指定了chatId，只搜索该聊天的消息
          if (chatId && msg.chatid !== Number(chatId)) return false

          // 如果指定了isGroup，只搜索对应类型的消息
          if (isGroup !== null) {
            const expectedTypecode = isGroup ? 2 : 1
            if (msg.typecode !== expectedTypecode) return false
          }

          // 搜索消息内容
          const messageContent = (msg.msg || '').toLowerCase()
          return messageContent.includes(searchTerm)
        })

        // 按时间排序
        filteredMessages.sort((a, b) => {
          const timeA = a.timestamp || new Date(a.t).getTime()
          const timeB = b.timestamp || new Date(b.t).getTime()
          return timeB - timeA // 最新的在前
        })

        console.log(`搜索聊天记录: 关键词="${keyword}", 结果${filteredMessages.length}条`)
        resolve(filteredMessages)
      }

      request.onerror = () => {
        console.error('搜索聊天记录失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('搜索聊天记录失败:', error)
    throw error
  }
}

/**
 * 标记消息为已读
 * @param {string} chatId - 聊天对象ID
 * @returns {Promise<boolean>}
 */
export const markMessagesAsRead = async (chatId) => {
  try {
    if (!chatId) {
      throw new Error('chatId is required')
    }

    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())
      const index = store.index('chatid')

      const request = index.getAll(chatId.toString())

      request.onsuccess = () => {
        const messages = request.result || []
        const unreadMessages = messages.filter(m => m.isRedRead === 0)

        if (unreadMessages.length === 0) {
          resolve(true)
          return
        }

        let updateCount = 0

        unreadMessages.forEach(message => {
          message.isRedRead = 1

          const updateRequest = store.put(message)

          updateRequest.onsuccess = () => {
            updateCount++
            if (updateCount === unreadMessages.length) {
              console.log(`标记消息为已读: chatId=${chatId}, 更新数量=${updateCount}`)
              resolve(true)
            }
          }

          updateRequest.onerror = () => {
            console.error('更新消息已读状态失败:', updateRequest.error)
            reject(updateRequest.error)
          }
        })
      }

      request.onerror = () => {
        console.error('获取消息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('标记消息为已读处理错误:', error)
    throw error
  }
}

/**
 * 清空所有数据
 * @returns {Promise<boolean>}
 */
export const clearAllData = async () => {
  try {
    // 确保对象存储存在
    await ensureObjectStore()
    const tableName = getTableName()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)

      const clearRequest = store.clear()

      clearRequest.onsuccess = () => {
        console.log('数据库已清空')
        resolve(true)
      }

      clearRequest.onerror = () => {
        console.error('清空数据库失败:', clearRequest.error)
        reject(clearRequest.error)
      }
    })
  } catch (error) {
    console.error('清空数据库异常:', error)
    throw error
  }
}

/**
 * 根据消息ID获取消息
 * @param {string} messageId - 消息ID
 * @returns {Promise<Object|null>}
 */
export const getMessageById = async (messageId) => {
  try {
    if (!messageId) {
      throw new Error('messageId is required')
    }

    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const request = store.get(String(messageId))

      request.onsuccess = () => {
        const message = request.result
        console.log('获取消息成功:', messageId, message ? '找到' : '未找到')
        resolve(message || null)
      }

      request.onerror = () => {
        console.error('获取消息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取消息处理错误:', error)
    throw error
  }
}

/**
 * 更新消息
 * @param {string} messageId - 消息ID
 * @param {Object} updates - 要更新的字段
 * @returns {Promise<Object>}
 */
export const updateMessage = async (messageId, updates) => {
  try {
    if (!messageId || !updates) {
      throw new Error('messageId and updates are required')
    }

    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())

      // 先获取现有消息
      const getRequest = store.get(String(messageId))

      getRequest.onsuccess = () => {
        const existingMessage = getRequest.result
        if (!existingMessage) {
          reject(new Error('消息不存在'))
          return
        }

        // 合并更新
        const updatedMessage = { ...existingMessage, ...updates }

        // 确保ID不被修改
        updatedMessage.id = String(messageId)

        const putRequest = store.put(updatedMessage)

        putRequest.onsuccess = () => {
          console.log('消息更新成功:', messageId)
          resolve(updatedMessage)
        }

        putRequest.onerror = () => {
          console.error('消息更新失败:', putRequest.error)
          reject(putRequest.error)
        }
      }

      getRequest.onerror = () => {
        console.error('获取消息失败:', getRequest.error)
        reject(getRequest.error)
      }
    })
  } catch (error) {
    console.error('更新消息处理错误:', error)
    throw error
  }
}

/**
 * 删除消息（软删除，设置idDel=1）
 * @param {string} messageId - 消息ID
 * @returns {Promise<boolean>}
 */
export const deleteMessage = async (messageId) => {
  try {
    await updateMessage(messageId, { idDel: 1 })
    console.log('消息删除成功:', messageId)
    return true
  } catch (error) {
    console.error('删除消息失败:', error)
    throw error
  }
}

/**
 * 撤回消息（设置typecode2=5）
 * @param {string} messageId - 消息ID
 * @param {Object} retractInfo - 撤回信息 {ret: 撤回的消息id, other: 自定义}
 * @returns {Promise<boolean>}
 */
export const retractMessage = async (messageId, retractInfo = {}) => {
  try {
    const retractData = {
      typecode2: 5,
      msg: JSON.stringify({ ret: messageId, ...retractInfo })
    }
    await updateMessage(messageId, retractData)
    console.log('消息撤回成功:', messageId)
    return true
  } catch (error) {
    console.error('撤回消息失败:', error)
    throw error
  }
}

/**
 * 获取未读消息数量
 * @param {string} chatId - 聊天对象ID（可选，不传则获取所有未读数量）
 * @returns {Promise<number>}
 */
export const getUnreadCount = async (chatId = null) => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const index = store.index('isRedRead')

      const request = index.getAll(0) // 获取所有未读消息

      request.onsuccess = () => {
        let messages = request.result || []

        // 如果指定了chatId，过滤该聊天的消息
        if (chatId) {
          messages = messages.filter(msg => msg.chatid === Number(chatId))
        }

        const unreadCount = messages.length
        console.log(`未读消息数量: ${chatId ? `chatId=${chatId}` : '全部'} = ${unreadCount}`)
        resolve(unreadCount)
      }

      request.onerror = () => {
        console.error('获取未读消息数量失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取未读消息数量处理错误:', error)
    throw error
  }
}

/**
 * 初始化数据库（应用启动时调用）
 * @returns {Promise<boolean>}
 */
export const initDatabase = async () => {
  try {
    console.log('开始初始化IndexedDB数据库...')

    // 设置用户ID（如果localStorage中有的话）
    const userId = localStorage.getItem('userId') || sessionStorage.getItem('userId')
    if (userId) {
      console.log('找到用户ID:', userId)
    } else {
      console.log('未找到用户ID，使用默认值')
    }

    // 打开数据库
    await openDb()
    console.log('数据库初始化成功')

    // 检查数据库中是否有数据
    const chatList = await getChatList()
    console.log(`数据库中现有聊天记录数量: ${chatList.length}`)

    // 清理过期的用户信息缓存
    try {
      const deletedCount = await clearExpiredUserInfo()
      if (deletedCount > 0) {
        console.log(`清理了 ${deletedCount} 条过期用户信息缓存`)
      }
    } catch (error) {
      console.warn('清理过期用户信息缓存失败:', error)
    }

    return true
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return false
  }
}

/**
 * 检查数据库连接状态
 * @returns {boolean}
 */
export const isDatabaseConnected = () => {
  return db !== null && db.readyState !== 'closed'
}

/**
 * 保存用户信息到数据库
 * @param {Object} userInfo - 用户信息
 * @returns {Promise<Object>}
 */
export const saveUserInfo = async (userInfo) => {
  try {
    await ensureObjectStore()

    const userInfoWithTimestamp = {
      ...userInfo,
      timestamp: Date.now()
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getUserInfoTableName()], 'readwrite')
      const store = transaction.objectStore(getUserInfoTableName())
      const request = store.put(userInfoWithTimestamp)

      request.onsuccess = () => {
        console.log('用户信息保存成功:', userInfo.id)
        resolve(userInfoWithTimestamp)
      }

      request.onerror = () => {
        console.error('用户信息保存失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('保存用户信息失败:', error)
    throw error
  }
}

/**
 * 从数据库获取用户信息
 * @param {string|number} userId - 用户ID
 * @returns {Promise<Object|null>}
 */
export const getUserInfoFromDB = async (userId) => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getUserInfoTableName()], 'readonly')
      const store = transaction.objectStore(getUserInfoTableName())
      const request = store.get(String(userId))

      request.onsuccess = () => {
        const result = request.result
        if (result) {
          // 检查缓存是否过期（30分钟）
          const isExpired = Date.now() - result.timestamp > 30 * 60 * 1000
          if (isExpired) {
            console.log('用户信息缓存已过期:', userId)
            resolve(null)
          } else {
            console.log('从数据库获取用户信息成功:', userId)
            resolve(result)
          }
        } else {
          console.log('数据库中未找到用户信息:', userId)
          resolve(null)
        }
      }

      request.onerror = () => {
        console.error('获取用户信息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('从数据库获取用户信息失败:', error)
    return null
  }
}

/**
 * 批量获取用户信息
 * @param {Array<string|number>} userIds - 用户ID数组
 * @returns {Promise<Map>}
 */
export const getBatchUserInfoFromDB = async (userIds) => {
  try {
    await ensureObjectStore()

    const result = new Map()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getUserInfoTableName()], 'readonly')
      const store = transaction.objectStore(getUserInfoTableName())

      let completed = 0
      const total = userIds.length

      if (total === 0) {
        resolve(result)
        return
      }

      userIds.forEach(userId => {
        const request = store.get(String(userId))

        request.onsuccess = () => {
          const userInfo = request.result
          if (userInfo) {
            // 检查缓存是否过期
            const isExpired = Date.now() - userInfo.timestamp > 30 * 60 * 1000
            if (!isExpired) {
              result.set(String(userId), userInfo)
            }
          }

          completed++
          if (completed === total) {
            resolve(result)
          }
        }

        request.onerror = () => {
          completed++
          if (completed === total) {
            resolve(result)
          }
        }
      })
    })
  } catch (error) {
    console.error('批量获取用户信息失败:', error)
    return new Map()
  }
}

/**
 * 清除过期的用户信息缓存
 * @returns {Promise<number>} 清除的记录数
 */
export const clearExpiredUserInfo = async () => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getUserInfoTableName()], 'readwrite')
      const store = transaction.objectStore(getUserInfoTableName())
      const index = store.index('timestamp')

      const expireTime = Date.now() - 30 * 60 * 1000 // 30分钟前
      const range = IDBKeyRange.upperBound(expireTime)
      const request = index.openCursor(range)

      let deletedCount = 0

      request.onsuccess = (event) => {
        const cursor = event.target.result
        if (cursor) {
          cursor.delete()
          deletedCount++
          cursor.continue()
        } else {
          console.log(`清除了 ${deletedCount} 条过期用户信息`)
          resolve(deletedCount)
        }
      }

      request.onerror = () => {
        console.error('清除过期用户信息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('清除过期用户信息失败:', error)
    return 0
  }
}

/**
 * 重新连接数据库
 * @returns {Promise<boolean>}
 */
export const reconnectDatabase = async () => {
  try {
    console.log('重新连接数据库...')
    if (db) {
      db.close()
      db = null
    }
    await openDb()
    console.log('数据库重连成功')
    return true
  } catch (error) {
    console.error('数据库重连失败:', error)
    return false
  }
}

/**
 * 调试函数：检查数据库中的所有数据
 * @returns {Promise<Array>}
 */
export const debugAllMessages = async () => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const request = store.getAll()

      request.onsuccess = () => {
        const messages = request.result || []
        console.log('=== 数据库中的所有消息 ===')
        console.log(`总消息数: ${messages.length}`)

        if (messages.length > 0) {
          // 按chatid分组显示
          const chatGroups = {}
          messages.forEach(msg => {
            const chatId = msg.chatid
            if (!chatGroups[chatId]) {
              chatGroups[chatId] = []
            }
            chatGroups[chatId].push(msg)
          })

          console.log('按chatid分组:')
          Object.keys(chatGroups).forEach(chatId => {
            console.log(`  chatId: ${chatId} (类型: ${typeof chatId}) - ${chatGroups[chatId].length} 条消息`)
            console.log(`    示例消息:`, chatGroups[chatId][0])
          })
        }

        resolve(messages)
      }

      request.onerror = () => {
        console.error('调试查询失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('调试函数错误:', error)
    throw error
  }
}
