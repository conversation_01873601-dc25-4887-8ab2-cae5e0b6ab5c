/**
 * WebSocket 连接服务
 * 处理 WebSocket 消息接收、解密、用户信息获取和数据库存储
 * 参考 chat-app/utils/connectionService.js 的逻辑
 */

import { addTabItem, addTab } from './db.js'
import { decryptAESBase64 } from './decrypt.js'
import webSocketManager from './websocket.js'
import { useWebSocketStore } from '@/pinia/modules/websocket.js'
import { useUserStore } from '@/pinia/modules/user.js'
import { ElNotification } from 'element-plus'
import { emitter } from '@/utils/bus.js'

// 用户信息缓存
const userInfoCache = new Map()

/**
 * 初始化连接服务
 */
export const initConnectionService = async () => {
  try {
    // 确保数据库表已创建
    await addTab()
    
    // 设置消息监听器
    setupMessageListener()
    
    console.log('连接服务初始化成功')
    return true
  } catch (error) {
    console.error('连接服务初始化失败:', error)
    return false
  }
}

/**
 * 设置消息监听器
 */
export const setupMessageListener = () => {
  // 监听所有 WebSocket 消息
  webSocketManager.addMessageHandler('message', async (data) => {
    try {
      console.log('收到WebSocket消息:', data)
      
      // 处理不同类型的消息
      await handleIncomingMessage(data)
      
    } catch (error) {
      console.error('处理WebSocket消息失败:', error)
    }
  })
}

/**
 * 处理接收到的消息
 * @param {Object} data - 消息数据
 */
const handleIncomingMessage = async (data) => {
  try {
    const { typecode } = data

    // 根据消息类型进行处理
    switch (typecode) {
      case 1: // 私聊消息
        await handlePrivateMessage(data)
        break
      case 2: // 群聊消息
        await handleGroupMessage(data)
        break
      case 3: // 通知消息
        await handleNotificationMessage(data)
        break
      default:
        console.log('未知消息类型:', typecode, data)
    }
    
  } catch (error) {
    console.error('处理消息失败:', error)
  }
}

/**
 * 处理私聊消息
 * @param {Object} data - 消息数据
 */
const handlePrivateMessage = async (data) => {
  try {
    console.log('处理私聊消息:', data)

    // 检查是否是自己发送的消息
    const userStore = useUserStore()
    const currentUserId = userStore.userInfo?.id || userStore.userInfo?.userId
    const isOwnMessage = data.fromid == currentUserId

    console.log('消息发送者ID:', data.fromid, '当前用户ID:', currentUserId, '是否为自己的消息:', isOwnMessage)

    // 解密消息内容
    let decryptedMsg = data.msg
    if (data.msg && typeof data.msg === 'string') {
      try {
        decryptedMsg = decryptAESBase64(data.msg)
      } catch (decryptError) {
        console.warn('消息解密失败，使用原始消息:', decryptError)
        decryptedMsg = data.msg
      }
    }

    // 获取发送者信息
    const senderInfo = await getUserInfo(data.fromid)

    // 构建消息对象
    const messageItem = {
      id: data.id || Date.now(),
      typecode: 1,
      typecode2: data.typecode2 || 0,
      toid: data.toid,
      fromid: data.fromid,
      chatid: Math.min(parseInt(data.fromid), parseInt(currentUserId)).toString(), // 私聊时使用较小的用户ID作为chatid
      t: data.t || new Date().toISOString(),
      msg: decryptedMsg,
      isRedRead: isOwnMessage ? 1 : 0, // 自己的消息标记为已读
      idDel: 0, // 未删除
      senderAvatar: senderInfo.avatar || '',
      senderNickname: senderInfo.nickname || `用户${data.fromid}`,
      avatar: senderInfo.avatar || '',
      nickname: senderInfo.nickname || `用户${data.fromid}`,
      lastMessage: decryptedMsg,
      timestamp: new Date().getTime(),
      unreadCount: isOwnMessage ? 0 : 1, // 自己的消息不增加未读数
      isOwnMessage // 添加标识字段
    }

    // 保存到数据库
    await addTabItem(messageItem)

    // 只有别人发送的消息才更新状态和显示通知
    if (!isOwnMessage) {
      // 更新 Pinia 状态
      updateStoreWithMessage(messageItem)

      // 显示通知（需要检查聊天界面状态）
      showMessageNotification(messageItem, '私聊消息')
    }

    console.log('私聊消息处理完成:', messageItem)

  } catch (error) {
    console.error('处理私聊消息失败:', error)
  }
}

/**
 * 处理群聊消息
 * @param {Object} data - 消息数据
 */
const handleGroupMessage = async (data) => {
  try {
    console.log('处理群聊消息:', data)

    // 检查是否是自己发送的消息
    const userStore = useUserStore()
    const currentUserId = userStore.userInfo?.id || userStore.userInfo?.userId
    const isOwnMessage = data.fromid == currentUserId

    console.log('群聊消息发送者ID:', data.fromid, '当前用户ID:', currentUserId, '是否为自己的消息:', isOwnMessage)

    // 解密消息内容
    let decryptedMsg = data.msg
    if (data.msg && typeof data.msg === 'string') {
      try {
        decryptedMsg = decryptAESBase64(data.msg)
      } catch (decryptError) {
        console.warn('消息解密失败，使用原始消息:', decryptError)
        decryptedMsg = data.msg
      }
    }

    // 从群成员信息中获取发送者信息
    const { getUserInfoFromGroupMembers } = await import('@/utils/avatarService.js')
    const senderInfo = await getUserInfoFromGroupMembers(data.fromid, data.groupID)

    // 构建消息对象
    const messageItem = {
      id: data.id || Date.now(),
      typecode: 2,
      typecode2: data.typecode2 || 0,
      toid: data.toid,
      fromid: data.fromid,
      chatid: data.groupID || data.toid, // 群聊时 chatid 为群组ID
      t: data.t || new Date().toISOString(),
      msg: decryptedMsg,
      isRedRead: isOwnMessage ? 1 : 0, // 自己的消息标记为已读
      idDel: 0, // 未删除
      senderAvatar: senderInfo.avatar || '',
      senderNickname: senderInfo.nickname || `用户${data.fromid}`,
      avatar: senderInfo.avatar || '',
      nickname: senderInfo.nickname || `群聊${data.groupID || data.toid}`,
      lastMessage: decryptedMsg,
      timestamp: new Date().getTime(),
      unreadCount: isOwnMessage ? 0 : 1, // 自己的消息不增加未读数
      isOwnMessage // 添加标识字段
    }

    // 保存到数据库
    await addTabItem(messageItem)

    // 只有别人发送的消息才更新状态和显示通知
    if (!isOwnMessage) {
      // 更新 Pinia 状态
      updateStoreWithMessage(messageItem)

      // 显示通知（需要检查聊天界面状态）
      showMessageNotification(messageItem, '群聊消息')
    }

    console.log('群聊消息处理完成:', messageItem)

  } catch (error) {
    console.error('处理群聊消息失败:', error)
  }
}

/**
 * 处理通知消息
 * @param {Object} data - 消息数据
 */
const handleNotificationMessage = async (data) => {
  try {
    console.log('处理通知消息:', data)
    
    // 构建通知消息对象
    const messageItem = {
      id: data.id || Date.now(),
      typecode: 3,
      typecode2: data.typecode2 || 0,
      toid: data.toid,
      fromid: data.fromid,
      chatid: `notification_${data.fromid}`,
      t: data.t || new Date().toISOString(),
      msg: data.msg || '系统通知',
      isRedRead: 0, // 未读
      idDel: 0, // 未删除
      senderAvatar: '/static/My/avatar.jpg',
      senderNickname: '系统通知',
      avatar: '/static/My/avatar.jpg',
      nickname: '系统通知',
      lastMessage: data.msg || '系统通知',
      timestamp: new Date().getTime(),
      unreadCount: 1
    }
    
    // 保存到数据库
    await addTabItem(messageItem)
    
    // 更新 Pinia 状态
    updateStoreWithMessage(messageItem)
    
    // 显示通知
    showMessageNotification(messageItem, '系统通知')
    
    console.log('通知消息处理完成:', messageItem)
    
  } catch (error) {
    console.error('处理通知消息失败:', error)
  }
}

/**
 * 获取用户信息
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>}
 */
const getUserInfo = async (userId) => {
  try {
    // 使用新的avatarService获取用户信息
    const { getUserInfo: getAvatarUserInfo } = await import('@/utils/avatarService.js')
    return await getAvatarUserInfo(userId)
  } catch (error) {
    console.error('获取用户信息失败:', error)
    const { getDefaultAvatar } = await import('@/utils/avatarService.js')
    return {
      id: userId,
      nickname: `用户${userId}`,
      avatar: getDefaultAvatar()
    }
  }
}

// 聊天界面状态管理
let chatDialogOpenState = false

/**
 * 设置聊天界面状态
 * @param {boolean} isOpen - 是否打开
 */
export const setChatDialogState = (isOpen) => {
  chatDialogOpenState = isOpen
  console.log('聊天界面状态已更新:', isOpen ? '打开' : '关闭')
}

/**
 * 检查聊天界面是否打开
 * @returns {boolean} 聊天界面是否打开
 */
const isChatDialogOpen = () => {
  return chatDialogOpenState
}

/**
 * 更新 Pinia 状态
 * @param {Object} messageItem - 消息对象
 */
const updateStoreWithMessage = (messageItem) => {
  try {
    const webSocketStore = useWebSocketStore()

    // 更新最新消息
    webSocketStore.lastMessage = messageItem

    // 只有在聊天界面关闭时才增加未读计数
    if (!isChatDialogOpen()) {
      webSocketStore.unreadCount++
      console.log('聊天界面已关闭，增加未读计数:', webSocketStore.unreadCount)
    } else {
      console.log('聊天界面已打开，不增加未读计数')
    }

    // 触发消息更新事件
    emitter.emit('messageReceived', messageItem)

  } catch (error) {
    console.error('更新Pinia状态失败:', error)
  }
}

/**
 * 显示消息通知
 * @param {Object} messageItem - 消息对象
 * @param {string} type - 消息类型
 */
const showMessageNotification = (messageItem, type) => {
  try {
    // 只有在聊天界面关闭时才显示通知
    if (!isChatDialogOpen()) {
      // 根据消息类型显示不同的内容
      let displayMessage = messageItem.msg
      switch (messageItem.typecode2) {
        case 2:
          displayMessage = '[图片]'
          break
        case 1:
          displayMessage = '[语音]'
          break
        case 3:
          displayMessage = '[视频]'
          break
        case 4:
          displayMessage = '[文件]'
          break
        default:
          displayMessage = messageItem.msg
      }

      ElNotification({
        title: type,
        message: `${messageItem.senderNickname}: ${displayMessage}`,
        type: 'info',
        duration: 3000,
        onClick: () => {
          // 点击通知时打开聊天弹窗
          console.log('点击通知，打开聊天弹窗:', messageItem)
          emitter.emit('openChatDialog', {
            id: messageItem.fromid,
            name: messageItem.senderNickname,
            headImg: messageItem.senderAvatar
          })
        }
      })
      console.log('聊天界面已关闭，显示通知:', type, displayMessage)
    } else {
      console.log('聊天界面已打开，不显示通知')
    }
  } catch (error) {
    console.error('显示通知失败:', error)
  }
}

/**
 * 清除用户信息缓存
 */
export const clearUserInfoCache = () => {
  userInfoCache.clear()
  console.log('用户信息缓存已清除')
}

/**
 * 获取缓存的用户信息
 * @param {string} userId - 用户ID
 * @returns {Object|null}
 */
export const getCachedUserInfo = (userId) => {
  return userInfoCache.get(userId) || null
}
