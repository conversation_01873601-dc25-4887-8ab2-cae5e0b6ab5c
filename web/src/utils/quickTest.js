/**
 * 快速测试脚本
 * 用于验证语法错误修复和基本功能
 */

/**
 * 测试简化版数据库
 */
export const testSimpleDB = async () => {
  try {
    console.log('🧪 测试简化版数据库...')
    
    const { initDatabaseSafe, addMessage, getMessages } = await import('./dbSimple.js')
    
    // 初始化数据库
    const initSuccess = await initDatabaseSafe()
    if (!initSuccess) {
      throw new Error('数据库初始化失败')
    }
    
    console.log('✅ 简化版数据库初始化成功')
    
    // 测试添加消息
    const testMessage = {
      id: 'test_' + Date.now(),
      chatid: 1001,
      fromid: 2001,
      toid: 1001,
      msg: '这是一条测试消息',
      typecode: 2,
      typecode2: 0
    }
    
    const savedMessage = await addMessage(testMessage)
    console.log('✅ 消息添加成功:', savedMessage.id)
    
    // 测试获取消息
    const messages = await getMessages(1001, 10)
    console.log('✅ 消息获取成功:', messages.length, '条')
    
    return {
      success: true,
      messageCount: messages.length
    }
  } catch (error) {
    console.error('❌ 简化版数据库测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试初始化管理器
 */
export const testInitManager = async () => {
  try {
    console.log('🧪 测试初始化管理器...')
    
    const { initManager } = await import('./initManager.js')
    
    // 获取状态
    const status = initManager.getStatus()
    console.log('初始化管理器状态:', status)
    
    // 如果未初始化，尝试初始化
    if (!status.isInitialized) {
      console.log('开始初始化...')
      const success = await initManager.initialize()
      console.log('初始化结果:', success)
      
      return {
        success,
        status: initManager.getStatus()
      }
    } else {
      console.log('✅ 初始化管理器已初始化')
      return {
        success: true,
        status
      }
    }
  } catch (error) {
    console.error('❌ 初始化管理器测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试语法检查
 */
export const testSyntax = async () => {
  const results = {
    db: { success: false, error: null },
    chatManager: { success: false, error: null },
    initManager: { success: false, error: null },
    dbSimple: { success: false, error: null }
  }
  
  // 测试 db.js
  try {
    await import('./db.js')
    results.db.success = true
    console.log('✅ db.js 语法检查通过')
  } catch (error) {
    results.db.error = error.message
    console.error('❌ db.js 语法错误:', error.message)
  }
  
  // 测试 chatManager.js
  try {
    await import('./chatManager.js')
    results.chatManager.success = true
    console.log('✅ chatManager.js 语法检查通过')
  } catch (error) {
    results.chatManager.error = error.message
    console.error('❌ chatManager.js 语法错误:', error.message)
  }
  
  // 测试 initManager.js
  try {
    await import('./initManager.js')
    results.initManager.success = true
    console.log('✅ initManager.js 语法检查通过')
  } catch (error) {
    results.initManager.error = error.message
    console.error('❌ initManager.js 语法错误:', error.message)
  }
  
  // 测试 dbSimple.js
  try {
    await import('./dbSimple.js')
    results.dbSimple.success = true
    console.log('✅ dbSimple.js 语法检查通过')
  } catch (error) {
    results.dbSimple.error = error.message
    console.error('❌ dbSimple.js 语法错误:', error.message)
  }
  
  const allPassed = Object.values(results).every(result => result.success)
  
  console.log('📊 语法检查结果:')
  console.log('db.js:', results.db.success ? '✅' : '❌')
  console.log('chatManager.js:', results.chatManager.success ? '✅' : '❌')
  console.log('initManager.js:', results.initManager.success ? '✅' : '❌')
  console.log('dbSimple.js:', results.dbSimple.success ? '✅' : '❌')
  
  if (allPassed) {
    console.log('🎉 所有文件语法检查通过！')
  } else {
    console.log('⚠️ 部分文件存在语法错误')
  }
  
  return {
    allPassed,
    results
  }
}

/**
 * 运行快速测试
 */
export const runQuickTest = async () => {
  console.log('🚀 开始快速测试...')
  console.log('=' .repeat(50))
  
  // 1. 语法检查
  console.log('1️⃣ 语法检查...')
  const syntaxResult = await testSyntax()
  
  if (!syntaxResult.allPassed) {
    console.log('❌ 语法检查失败，停止后续测试')
    return {
      success: false,
      step: 'syntax',
      results: { syntax: syntaxResult }
    }
  }
  
  // 2. 简化数据库测试
  console.log('\n2️⃣ 简化数据库测试...')
  const dbResult = await testSimpleDB()
  
  // 3. 初始化管理器测试
  console.log('\n3️⃣ 初始化管理器测试...')
  const initResult = await testInitManager()
  
  const allResults = {
    syntax: syntaxResult,
    database: dbResult,
    initManager: initResult
  }
  
  const overallSuccess = syntaxResult.allPassed && dbResult.success && initResult.success
  
  console.log('\n' + '=' .repeat(50))
  console.log('📊 快速测试结果:')
  console.log('语法检查:', syntaxResult.allPassed ? '✅' : '❌')
  console.log('数据库:', dbResult.success ? '✅' : '❌')
  console.log('初始化管理器:', initResult.success ? '✅' : '❌')
  
  if (overallSuccess) {
    console.log('🎉 快速测试全部通过！')
  } else {
    console.log('⚠️ 部分测试失败，请检查错误信息')
  }
  
  return {
    success: overallSuccess,
    results: allResults
  }
}

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.quickTest = runQuickTest
  window.testSyntax = testSyntax
  window.testSimpleDB = testSimpleDB
  window.testInitManager = testInitManager
  
  console.log('⚡ 快速测试工具已加载:')
  console.log('  - window.quickTest() - 运行快速测试')
  console.log('  - window.testSyntax() - 语法检查')
  console.log('  - window.testSimpleDB() - 测试简化数据库')
  console.log('  - window.testInitManager() - 测试初始化管理器')
}
