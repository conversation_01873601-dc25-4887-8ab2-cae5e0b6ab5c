/**
 * WebSocket 连接管理类
 * 用于管理与服务器的WebSocket连接
 * 集成消息处理和数据库存储功能
 */
import { initConnectionService } from './connectionService.js'

class WebSocketManager {
  constructor() {
    this.ws = null
    this.url = ''
    this.token = ''
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = Infinity // 无限重连
    this.reconnectInterval = 5000 // 初始重连间隔5秒
    this.maxReconnectInterval = 60000 // 最大重连间隔60秒
    this.heartbeatInterval = null
    this.heartbeatTimer = 30000 // 30秒心跳
    this.isManualClose = false
    this.messageHandlers = new Map()
    this.connectionStatus = 'disconnected' // disconnected, connecting, connected
    this.reconnectTimer = null // 重连定时器
    this.persistentConnection = false // 是否启用持久连接
    this.visibilityChangeHandler = null // 页面可见性变化处理器
    this.networkChangeHandler = null // 网络状态变化处理器
  }

  /**
   * 初始化WebSocket连接
   * @param {string} token - 用户token
   * @param {boolean} persistent - 是否启用持久连接
   */
  connect(token, persistent = true) {
    if (!token) {
      console.error('WebSocket连接失败：token不能为空')
      return Promise.reject(new Error('token不能为空'))
    }

    // 优先使用 localStorage 中的 setToken，如果没有则使用传入的 token
    const setToken = localStorage.getItem('setToken')
    this.token = setToken || token
    this.url = `ws://43.198.105.182:82/api/msgSocket?token=${this.token}`
    this.isManualClose = false
    this.persistentConnection = persistent

    // 启用持久连接时设置监听器
    if (persistent) {
      this.setupPersistentConnection()
    }

    return new Promise((resolve, reject) => {
      try {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          console.log('WebSocket已连接，无需重复连接')
          resolve()
          return
        }

        this.connectionStatus = 'connecting'
        console.log('正在连接WebSocket...', this.url)
        
        this.ws = new WebSocket(this.url)

        // 连接成功
        this.ws.onopen = async () => {
          console.log('WebSocket连接成功')
          this.connectionStatus = 'connected'
          this.reconnectAttempts = 0
          this.reconnectInterval = 5000 // 重置重连间隔
          this.startHeartbeat()

          // 初始化连接服务
          try {
            await initConnectionService()
            console.log('连接服务初始化成功')
          } catch (error) {
            console.error('连接服务初始化失败:', error)
          }

          resolve()
        }

        // 接收消息
        this.ws.onmessage = (event) => {
          console.log('收到WebSocket消息:', event)
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('WebSocket消息解析失败:', error, event.data)
          }
        }

        // 连接错误
        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.connectionStatus = 'disconnected'
          // 发生错误时尝试重连
          this.scheduleReconnect()
          reject(error)
        }

        // 连接关闭
        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭:', event.code, event.reason)
          this.connectionStatus = 'disconnected'
          this.stopHeartbeat()
          
          // 如果不是手动关闭，则尝试重连
          if (!this.isManualClose) {
            this.scheduleReconnect()
          }
        }

      } catch (error) {
        console.error('WebSocket连接异常:', error)
        this.connectionStatus = 'disconnected'
        // 连接异常时尝试重连
        this.scheduleReconnect()
        reject(error)
      }
    })
  }

  /**
   * 处理接收到的消息
   * @param {Object} data - 消息数据
   */
  handleMessage(data) {
    console.log('收到WebSocket消息:', data)

    // 心跳响应不需要特殊处理
    if (data.type === 'heartbeat' || data.type === 0) {
      return
    }

    // 触发消息处理器
    this.messageHandlers.forEach((handler, type) => {
      if (type === 'all' || type === 'message' || data.type === type || data.typecode === type) {
        try {
          handler(data)
        } catch (error) {
          console.error('消息处理器执行错误:', error)
        }
      }
    })
  }

  /**
   * 发送消息
   * @param {Object} data - 要发送的数据
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        const message = typeof data === 'string' ? data : JSON.stringify(data)
        this.ws.send(message)
        console.log('发送WebSocket消息:', data)
        return true
      } catch (error) {
        console.error('发送WebSocket消息失败:', error)
        return false
      }
    } else {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }
  }

  /**
   * 添加消息处理器
   * @param {string|number} type - 消息类型，'all'表示处理所有消息
   * @param {Function} handler - 处理函数
   */
  addMessageHandler(type, handler) {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   * @param {string|number} type - 消息类型
   */
  removeMessageHandler(type) {
    this.messageHandlers.delete(type)
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 0 }) // 发送心跳包
      }
    }, this.heartbeatTimer)
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    // 如果不是持久连接模式或手动关闭，则不重连
    if (!this.persistentConnection || this.isManualClose) {
      return
    }

    // 清除之前的重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.reconnectAttempts++

    // 计算重连间隔（指数退避，但有上限）
    const currentInterval = Math.min(
      this.reconnectInterval * Math.pow(1.5, Math.min(this.reconnectAttempts - 1, 10)),
      this.maxReconnectInterval
    )

    console.log(`WebSocket重连中... (第${this.reconnectAttempts}次尝试，${currentInterval/1000}秒后重连)`)

    this.reconnectTimer = setTimeout(() => {
      // 只要不是手动关闭且有token就尝试重连
      if (!this.isManualClose && this.token && this.persistentConnection) {
        this.connect(this.token, this.persistentConnection).catch(error => {
          console.error('WebSocket重连失败:', error)
          // 继续安排重连
          this.scheduleReconnect()
        })
      }
    }, currentInterval)
  }

  /**
   * 设置持久连接监听器
   */
  setupPersistentConnection() {
    // 页面可见性变化监听
    this.visibilityChangeHandler = () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时检查连接状态
        if (this.connectionStatus !== 'connected' && !this.isManualClose && this.token) {
          console.log('页面可见，检查WebSocket连接状态')
          this.connect(this.token, this.persistentConnection).catch(error => {
            console.error('页面可见时重连失败:', error)
          })
        }
      }
    }

    // 网络状态变化监听
    this.networkChangeHandler = () => {
      if (navigator.onLine && this.connectionStatus !== 'connected' && !this.isManualClose) {
        console.log('网络恢复，尝试重新连接WebSocket')
        setTimeout(() => {
          this.connect(this.token, this.persistentConnection).catch(error => {
            console.error('网络恢复时重连失败:', error)
          })
        }, 1000) // 延迟1秒重连
      }
    }

    // 添加事件监听器
    document.addEventListener('visibilitychange', this.visibilityChangeHandler)
    window.addEventListener('online', this.networkChangeHandler)

    // 页面加载完成后检查连接状态（处理页面刷新情况）
    if (document.readyState === 'complete') {
      this.checkConnectionAfterPageLoad()
    } else {
      window.addEventListener('load', () => {
        this.checkConnectionAfterPageLoad()
      }, { once: true })
    }

    console.log('持久连接监听器已设置')
  }

  /**
   * 页面加载完成后检查连接状态
   * 主要用于处理页面刷新后的重连
   */
  checkConnectionAfterPageLoad() {
    // 延迟检查，确保所有初始化完成
    setTimeout(() => {
      if (this.token && this.persistentConnection && this.connectionStatus !== 'connected' && !this.isManualClose) {
        console.log('页面加载完成，检测到需要重连WebSocket')
        this.connect(this.token, this.persistentConnection).catch(error => {
          console.log('页面加载后重连失败:', error.message)
        })
      }
    }, 1000) // 延迟1秒检查
  }

  /**
   * 移除持久连接监听器
   */
  removePersistentConnection() {
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      this.visibilityChangeHandler = null
    }

    if (this.networkChangeHandler) {
      window.removeEventListener('online', this.networkChangeHandler)
      this.networkChangeHandler = null
    }

    console.log('持久连接监听器已移除')
  }

  /**
   * 手动关闭连接
   */
  close() {
    this.isManualClose = true
    this.persistentConnection = false

    // 移除持久连接监听器
    this.removePersistentConnection()

    // 清除重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    this.stopHeartbeat()

    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    this.connectionStatus = 'disconnected'
    this.messageHandlers.clear()
    console.log('WebSocket连接已手动关闭')
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return this.connectionStatus
  }

  /**
   * 检查是否已连接
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 启用持久连接
   */
  enablePersistentConnection() {
    if (!this.persistentConnection) {
      this.persistentConnection = true
      this.setupPersistentConnection()
      console.log('持久连接已启用')

      // 如果当前未连接，尝试连接
      if (this.connectionStatus !== 'connected' && this.token && !this.isManualClose) {
        this.connect(this.token, true).catch(error => {
          console.error('启用持久连接时连接失败:', error)
        })
      }
    }
  }

  /**
   * 禁用持久连接
   */
  disablePersistentConnection() {
    if (this.persistentConnection) {
      this.persistentConnection = false
      this.removePersistentConnection()

      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }

      console.log('持久连接已禁用')
    }
  }

  /**
   * 强制重连
   * @param {string} token - 可选的token参数，如果不提供则使用保存的token
   */
  forceReconnect(token = null) {
    // 使用传入的token或保存的token
    const useToken = token || this.token

    if (useToken) {
      console.log('强制重连WebSocket')

      // 先关闭当前连接
      if (this.ws) {
        this.ws.close()
      }

      // 重置状态
      this.connectionStatus = 'disconnected'
      this.reconnectAttempts = 0

      // 立即尝试重连
      return this.connect(useToken, this.persistentConnection)
    } else {
      return Promise.reject(new Error('无法重连：token为空，请先登录'))
    }
  }
}

// 创建全局WebSocket管理器实例
const webSocketManager = new WebSocketManager()

export default webSocketManager