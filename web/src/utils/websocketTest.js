/**
 * WebSocket 连接测试工具
 * 用于测试持久连接功能
 */

import { useWebSocketStore } from '@/pinia/modules/websocket'
import { useUserStore } from '@/pinia/modules/user'

/**
 * 测试 WebSocket 连接状态
 */
export const testWebSocketConnection = () => {
  const webSocketStore = useWebSocketStore()
  const userStore = useUserStore()
  
  console.log('=== WebSocket 连接状态测试 ===')
  console.log('用户登录状态:', !!userStore.token)
  console.log('WebSocket 连接状态:', webSocketStore.connectionStatus)
  console.log('是否已连接:', webSocketStore.isConnected)
  console.log('是否连接中:', webSocketStore.isConnecting)
  console.log('自动重连启用:', webSocketStore.autoReconnectEnabled)
  console.log('未读消息数:', webSocketStore.unreadCount)
  console.log('================================')
  
  return {
    userLoggedIn: !!userStore.token,
    connectionStatus: webSocketStore.connectionStatus,
    isConnected: webSocketStore.isConnected,
    isConnecting: webSocketStore.isConnecting,
    autoReconnectEnabled: webSocketStore.autoReconnectEnabled,
    unreadCount: webSocketStore.unreadCount
  }
}

/**
 * 测试强制重连功能
 */
export const testForceReconnect = async () => {
  const webSocketStore = useWebSocketStore()
  const userStore = useUserStore()
  
  console.log('=== 测试强制重连功能 ===')
  
  if (!userStore.token) {
    console.error('用户未登录，无法测试重连')
    return false
  }
  
  try {
    console.log('开始强制重连测试...')
    await webSocketStore.forceReconnect()
    console.log('强制重连测试成功')
    return true
  } catch (error) {
    console.error('强制重连测试失败:', error)
    return false
  }
}

/**
 * 测试持久连接功能
 */
export const testPersistentConnection = () => {
  const webSocketStore = useWebSocketStore()
  
  console.log('=== 测试持久连接功能 ===')
  
  // 启用持久连接
  console.log('启用持久连接...')
  webSocketStore.enablePersistentConnection()
  
  // 等待一段时间后禁用
  setTimeout(() => {
    console.log('禁用持久连接...')
    webSocketStore.disablePersistentConnection()
  }, 5000)
  
  console.log('持久连接测试已启动，5秒后自动禁用')
}

/**
 * 模拟网络断开重连测试
 */
export const simulateNetworkReconnect = async () => {
  const webSocketStore = useWebSocketStore()
  
  console.log('=== 模拟网络断开重连测试 ===')
  
  // 先关闭连接
  console.log('模拟网络断开...')
  webSocketStore.closeConnection()
  
  // 等待2秒后尝试重连
  setTimeout(async () => {
    console.log('模拟网络恢复，尝试重连...')
    try {
      await webSocketStore.forceReconnect()
      console.log('网络恢复重连测试成功')
    } catch (error) {
      console.error('网络恢复重连测试失败:', error)
    }
  }, 2000)
  
  console.log('网络断开重连测试已启动')
}

/**
 * 在浏览器控制台中运行所有测试
 */
export const runAllTests = async () => {
  console.log('🚀 开始运行 WebSocket 测试套件...')
  
  // 测试1: 连接状态
  const status = testWebSocketConnection()
  
  // 测试2: 强制重连（如果用户已登录）
  if (status.userLoggedIn) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    await testForceReconnect()
  }
  
  // 测试3: 持久连接
  await new Promise(resolve => setTimeout(resolve, 1000))
  testPersistentConnection()
  
  // 测试4: 网络断开重连
  await new Promise(resolve => setTimeout(resolve, 6000))
  await simulateNetworkReconnect()
  
  console.log('✅ WebSocket 测试套件运行完成')
}

/**
 * 测试页面刷新后的重连
 */
export const testPageRefresh = () => {
  console.log('=== 测试页面刷新重连 ===')
  console.log('即将刷新页面，请观察刷新后的WebSocket重连情况')
  console.log('刷新后请在控制台查看重连日志')

  // 延迟刷新，给用户时间看到消息
  setTimeout(() => {
    window.location.reload()
  }, 2000)
}

// 在开发环境下将测试函数挂载到全局对象
if (process.env.NODE_ENV === 'development') {
  window.websocketTest = {
    testConnection: testWebSocketConnection,
    testReconnect: testForceReconnect,
    testPersistent: testPersistentConnection,
    simulateReconnect: simulateNetworkReconnect,
    testPageRefresh: testPageRefresh,
    runAll: runAllTests
  }

  console.log('🔧 WebSocket 测试工具已加载到 window.websocketTest')
  console.log('可用方法:')
  console.log('- window.websocketTest.testConnection() - 测试连接状态')
  console.log('- window.websocketTest.testReconnect() - 测试强制重连')
  console.log('- window.websocketTest.testPersistent() - 测试持久连接')
  console.log('- window.websocketTest.simulateReconnect() - 模拟网络重连')
  console.log('- window.websocketTest.testPageRefresh() - 测试页面刷新重连')
  console.log('- window.websocketTest.runAll() - 运行所有测试')
}
