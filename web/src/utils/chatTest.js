/**
 * 聊天系统测试文件
 * 用于测试聊天管理系统的各项功能
 */

import { chatManager } from './chatManager.js'

/**
 * 聊天系统测试类
 */
export class ChatSystemTest {
  constructor() {
    this.testResults = []
  }

  /**
   * 记录测试结果
   */
  logResult(testName, success, message = '') {
    const result = {
      test: testName,
      success,
      message,
      timestamp: new Date().toISOString()
    }
    this.testResults.push(result)
    
    const status = success ? '✅' : '❌'
    console.log(`${status} ${testName}: ${message}`)
  }

  /**
   * 测试1：初始化系统
   */
  async testInitialization() {
    try {
      const success = await chatManager.init()
      this.logResult('系统初始化', success, success ? '初始化成功' : '初始化失败')
      return success
    } catch (error) {
      this.logResult('系统初始化', false, `初始化异常: ${error.message}`)
      return false
    }
  }

  /**
   * 测试2：群聊列表管理
   */
  async testGroupManagement() {
    try {
      // 测试保存群聊列表
      const testGroups = [
        {
          id: 1001,
          name: '测试群聊1',
          GroupName: '测试群聊1',
          avatar: '/static/test_group1.jpg',
          GroupHeader: '/static/test_group1.jpg'
        },
        {
          id: 1002,
          name: '测试群聊2',
          GroupName: '测试群聊2',
          avatar: '/static/test_group2.jpg',
          GroupHeader: '/static/test_group2.jpg'
        }
      ]

      await chatManager.saveGroupList(testGroups)
      this.logResult('保存群聊列表', true, `保存了${testGroups.length}个群聊`)

      // 测试获取群聊列表
      const savedGroups = await chatManager.getGroupList()
      const success = savedGroups.length === testGroups.length
      this.logResult('获取群聊列表', success, `获取到${savedGroups.length}个群聊`)

      return success
    } catch (error) {
      this.logResult('群聊列表管理', false, `测试失败: ${error.message}`)
      return false
    }
  }

  /**
   * 测试3：私聊管理
   */
  async testPrivateChatManagement() {
    try {
      // 测试开始私聊
      const testUsers = [
        {
          id: 2001,
          name: '测试用户1',
          nickname: '测试用户1',
          avatar: '/static/test_user1.jpg'
        },
        {
          id: 2002,
          name: '测试用户2',
          nickname: '测试用户2',
          avatar: '/static/test_user2.jpg'
        }
      ]

      for (const user of testUsers) {
        await chatManager.startPrivateChat(user)
      }
      this.logResult('开始私聊', true, `创建了${testUsers.length}个私聊`)

      // 测试获取私聊列表
      const privateChats = await chatManager.getPrivateList()
      const success = privateChats.length === testUsers.length
      this.logResult('获取私聊列表', success, `获取到${privateChats.length}个私聊`)

      return success
    } catch (error) {
      this.logResult('私聊管理', false, `测试失败: ${error.message}`)
      return false
    }
  }

  /**
   * 测试4：消息发送和接收
   */
  async testMessageHandling() {
    try {
      const currentUserId = 3001
      let messageCount = 0

      // 测试群聊消息
      const groupMessages = [
        {
          id: `msg_group_${Date.now()}_1`,
          fromid: currentUserId,
          toid: 1001,
          chatid: 1001,
          msg: '这是第一条群聊测试消息',
          typecode2: 0
        },
        {
          id: `msg_group_${Date.now()}_2`,
          fromid: currentUserId,
          toid: 1001,
          chatid: 1001,
          msg: '这是第二条群聊测试消息',
          typecode2: 0
        }
      ]

      for (const message of groupMessages) {
        await chatManager.sendGroupMessage(message)
        messageCount++
      }

      // 测试私聊消息
      const privateMessages = [
        {
          id: `msg_private_${Date.now()}_1`,
          fromid: currentUserId,
          toid: 2001,
          chatid: 2001,
          msg: '这是第一条私聊测试消息',
          typecode2: 0
        },
        {
          id: `msg_private_${Date.now()}_2`,
          fromid: currentUserId,
          toid: 2001,
          chatid: 2001,
          msg: '这是第二条私聊测试消息',
          typecode2: 0
        }
      ]

      for (const message of privateMessages) {
        await chatManager.sendPrivateMessage(message)
        messageCount++
      }

      this.logResult('消息发送', true, `发送了${messageCount}条消息`)

      // 测试接收消息
      const receivedMessage = {
        id: `msg_received_${Date.now()}`,
        fromid: 2001,
        toid: currentUserId,
        chatid: 2001,
        msg: '这是接收到的测试消息',
        typecode: 1, // 私聊
        typecode2: 0,
        nickname: '测试用户1',
        avatar: '/static/test_user1.jpg'
      }

      await chatManager.receiveMessage(receivedMessage)
      this.logResult('消息接收', true, '接收消息成功')

      return true
    } catch (error) {
      this.logResult('消息处理', false, `测试失败: ${error.message}`)
      return false
    }
  }

  /**
   * 测试5：消息查询
   */
  async testMessageQuery() {
    try {
      // 测试获取群聊消息
      const groupMessages = await chatManager.getGroupMessages(1001, 1, 10)
      this.logResult('群聊消息查询', true, `获取到${groupMessages.length}条群聊消息`)

      // 测试获取私聊消息
      const privateMessages = await chatManager.getPrivateMessages(2001, 1, 10)
      this.logResult('私聊消息查询', true, `获取到${privateMessages.length}条私聊消息`)

      // 测试获取所有聊天列表
      const allChats = await chatManager.getAllChats()
      this.logResult('聊天列表查询', true, `获取到${allChats.length}个聊天`)

      return true
    } catch (error) {
      this.logResult('消息查询', false, `测试失败: ${error.message}`)
      return false
    }
  }

  /**
   * 测试6：消息搜索
   */
  async testMessageSearch() {
    try {
      // 全局搜索
      const globalResults = await chatManager.searchMessages('测试')
      this.logResult('全局消息搜索', true, `搜索到${globalResults.length}条消息`)

      // 群聊搜索
      const groupResults = await chatManager.searchMessages('群聊', 1001, true)
      this.logResult('群聊消息搜索', true, `搜索到${groupResults.length}条群聊消息`)

      // 私聊搜索
      const privateResults = await chatManager.searchMessages('私聊', 2001, false)
      this.logResult('私聊消息搜索', true, `搜索到${privateResults.length}条私聊消息`)

      return true
    } catch (error) {
      this.logResult('消息搜索', false, `测试失败: ${error.message}`)
      return false
    }
  }

  /**
   * 测试7：时间排序验证
   */
  async testTimeOrdering() {
    try {
      // 获取群聊消息并验证时间排序
      const messages = await chatManager.getGroupMessages(1001, 1, 20)
      
      if (messages.length > 1) {
        let isOrdered = true
        for (let i = 1; i < messages.length; i++) {
          const prevTime = messages[i-1].timestamp || new Date(messages[i-1].t).getTime()
          const currTime = messages[i].timestamp || new Date(messages[i].t).getTime()
          if (prevTime > currTime) {
            isOrdered = false
            break
          }
        }
        this.logResult('时间排序验证', isOrdered, isOrdered ? '消息按时间正序排列' : '消息时间排序错误')
        return isOrdered
      } else {
        this.logResult('时间排序验证', true, '消息数量不足，跳过排序验证')
        return true
      }
    } catch (error) {
      this.logResult('时间排序验证', false, `测试失败: ${error.message}`)
      return false
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行聊天系统完整测试')
    console.log('=' .repeat(50))

    const tests = [
      { name: '初始化测试', method: this.testInitialization },
      { name: '群聊管理测试', method: this.testGroupManagement },
      { name: '私聊管理测试', method: this.testPrivateChatManagement },
      { name: '消息处理测试', method: this.testMessageHandling },
      { name: '消息查询测试', method: this.testMessageQuery },
      { name: '消息搜索测试', method: this.testMessageSearch },
      { name: '时间排序测试', method: this.testTimeOrdering }
    ]

    let passedTests = 0
    let totalTests = tests.length

    for (const test of tests) {
      console.log(`\n📋 运行 ${test.name}...`)
      const result = await test.method.call(this)
      if (result) passedTests++
    }

    console.log('\n' + '=' .repeat(50))
    console.log(`📊 测试完成: ${passedTests}/${totalTests} 通过`)
    
    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！聊天系统运行正常')
    } else {
      console.log('⚠️  部分测试失败，请检查系统配置')
    }

    return {
      total: totalTests,
      passed: passedTests,
      results: this.testResults
    }
  }

  /**
   * 获取测试报告
   */
  getTestReport() {
    return {
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.success).length,
        failed: this.testResults.filter(r => !r.success).length
      },
      details: this.testResults
    }
  }
}

// 创建测试实例
export const chatTest = new ChatSystemTest()

// 导出快速测试函数
export const runQuickTest = async () => {
  return await chatTest.runAllTests()
}
