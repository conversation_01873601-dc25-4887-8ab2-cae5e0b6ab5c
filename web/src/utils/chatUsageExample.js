/**
 * 聊天管理系统使用示例
 * 展示如何使用 chatManager 进行聊天列表和记录的管理
 */

import { chatManager } from './chatManager.js'

/**
 * 使用示例类
 */
export class ChatUsageExample {
  
  /**
   * 1. 初始化聊天系统
   */
  async initChatSystem() {
    console.log('=== 初始化聊天系统 ===')
    
    try {
      const success = await chatManager.init()
      if (success) {
        console.log('✅ 聊天系统初始化成功')
      } else {
        console.log('❌ 聊天系统初始化失败')
      }
    } catch (error) {
      console.error('初始化失败:', error)
    }
  }

  /**
   * 2. 保存群聊列表（从接口获取群聊列表后调用）
   */
  async saveGroupListFromAPI() {
    console.log('=== 保存群聊列表 ===')
    
    // 模拟从接口获取的群聊列表
    const groupListFromAPI = [
      {
        id: 1001,
        name: '技术交流群',
        GroupName: '技术交流群',
        avatar: '/static/group1.jpg',
        GroupHeader: '/static/group1.jpg'
      },
      {
        id: 1002,
        name: '产品讨论群',
        GroupName: '产品讨论群',
        avatar: '/static/group2.jpg',
        GroupHeader: '/static/group2.jpg'
      }
    ]

    try {
      await chatManager.saveGroupList(groupListFromAPI)
      console.log('✅ 群聊列表保存成功')
      
      // 获取保存的群聊列表
      const savedGroups = await chatManager.getGroupList()
      console.log('保存的群聊列表:', savedGroups)
    } catch (error) {
      console.error('保存群聊列表失败:', error)
    }
  }

  /**
   * 3. 点击用户头像开始私聊
   */
  async startPrivateChatWithUser() {
    console.log('=== 开始私聊 ===')
    
    // 模拟点击用户头像的用户信息
    const userInfo = {
      id: 2001,
      name: '张三',
      nickname: '张三',
      avatar: '/static/user1.jpg',
      head_img: '/static/user1.jpg'
    }

    try {
      const privateChat = await chatManager.startPrivateChat(userInfo)
      console.log('✅ 私聊创建成功:', privateChat)
      
      // 获取私聊列表
      const privateList = await chatManager.getPrivateList()
      console.log('私聊列表:', privateList)
    } catch (error) {
      console.error('开始私聊失败:', error)
    }
  }

  /**
   * 4. 发送群聊消息
   */
  async sendGroupMessage() {
    console.log('=== 发送群聊消息 ===')
    
    const messageData = {
      id: `msg_${Date.now()}`,
      fromid: 3001, // 当前用户ID
      toid: 1001,   // 群聊ID
      chatid: 1001, // 群聊ID作为chatid
      msg: '大家好，这是一条群聊消息！',
      typecode2: 0, // 文本消息
      isRedRead: 0,
      idDel: 0,
      nickname: '当前用户',
      avatar: '/static/current_user.jpg'
    }

    try {
      const savedMessage = await chatManager.sendGroupMessage(messageData)
      console.log('✅ 群聊消息发送成功:', savedMessage)
    } catch (error) {
      console.error('发送群聊消息失败:', error)
    }
  }

  /**
   * 5. 发送私聊消息
   */
  async sendPrivateMessage() {
    console.log('=== 发送私聊消息 ===')
    
    const messageData = {
      id: `msg_${Date.now()}`,
      fromid: 3001, // 当前用户ID
      toid: 2001,   // 对方用户ID
      chatid: 2001, // 用户ID作为chatid
      msg: '你好，这是一条私聊消息！',
      typecode2: 0, // 文本消息
      isRedRead: 0,
      idDel: 0,
      nickname: '当前用户',
      avatar: '/static/current_user.jpg'
    }

    try {
      const savedMessage = await chatManager.sendPrivateMessage(messageData)
      console.log('✅ 私聊消息发送成功:', savedMessage)
    } catch (error) {
      console.error('发送私聊消息失败:', error)
    }
  }

  /**
   * 6. 接收WebSocket消息
   */
  async receiveWebSocketMessage() {
    console.log('=== 接收WebSocket消息 ===')
    
    // 模拟接收到的群聊消息
    const groupMessage = {
      id: `msg_${Date.now()}_received`,
      fromid: 2002,
      toid: 1001,
      chatid: 1001,
      msg: '这是接收到的群聊消息',
      typecode: 2, // 群聊类型
      typecode2: 0,
      isRedRead: 0,
      idDel: 0,
      senderNickname: '李四',
      senderAvatar: '/static/user2.jpg',
      t: new Date().toISOString()
    }

    // 模拟接收到的私聊消息
    const privateMessage = {
      id: `msg_${Date.now()}_received_private`,
      fromid: 2001,
      toid: 3001,
      chatid: 2001,
      msg: '这是接收到的私聊消息',
      typecode: 1, // 私聊类型
      typecode2: 0,
      isRedRead: 0,
      idDel: 0,
      nickname: '张三',
      avatar: '/static/user1.jpg',
      t: new Date().toISOString()
    }

    try {
      // 接收群聊消息
      const savedGroupMsg = await chatManager.receiveMessage(groupMessage)
      console.log('✅ 群聊消息接收成功:', savedGroupMsg)

      // 接收私聊消息
      const savedPrivateMsg = await chatManager.receiveMessage(privateMessage)
      console.log('✅ 私聊消息接收成功:', savedPrivateMsg)
    } catch (error) {
      console.error('接收消息失败:', error)
    }
  }

  /**
   * 7. 获取聊天记录
   */
  async getChatMessages() {
    console.log('=== 获取聊天记录 ===')
    
    try {
      // 获取群聊记录
      const groupMessages = await chatManager.getGroupMessages(1001, 1, 20)
      console.log('群聊记录:', groupMessages)

      // 获取私聊记录
      const privateMessages = await chatManager.getPrivateMessages(2001, 1, 20)
      console.log('私聊记录:', privateMessages)
    } catch (error) {
      console.error('获取聊天记录失败:', error)
    }
  }

  /**
   * 8. 获取所有聊天列表
   */
  async getAllChatList() {
    console.log('=== 获取所有聊天列表 ===')
    
    try {
      const allChats = await chatManager.getAllChats()
      console.log('所有聊天列表:', allChats)
      
      // 按类型分组显示
      const groups = allChats.filter(chat => chat.type === 'group')
      const privates = allChats.filter(chat => chat.type === 'private')
      
      console.log(`群聊 ${groups.length} 个:`, groups)
      console.log(`私聊 ${privates.length} 个:`, privates)
    } catch (error) {
      console.error('获取聊天列表失败:', error)
    }
  }

  /**
   * 9. 搜索聊天记录
   */
  async searchMessages() {
    console.log('=== 搜索聊天记录 ===')
    
    try {
      // 全局搜索
      const globalResults = await chatManager.searchMessages('消息')
      console.log('全局搜索结果:', globalResults)

      // 在特定群聊中搜索
      const groupResults = await chatManager.searchMessages('群聊', 1001, true)
      console.log('群聊搜索结果:', groupResults)

      // 在特定私聊中搜索
      const privateResults = await chatManager.searchMessages('私聊', 2001, false)
      console.log('私聊搜索结果:', privateResults)
    } catch (error) {
      console.error('搜索失败:', error)
    }
  }

  /**
   * 10. 清空和删除聊天
   */
  async clearAndDeleteChat() {
    console.log('=== 清空和删除聊天 ===')
    
    try {
      // 清空群聊记录（保留聊天列表）
      await chatManager.clearMessages(1001, true)
      console.log('✅ 群聊记录清空成功')

      // 删除私聊（删除记录和列表项）
      await chatManager.deleteChat(2001, false)
      console.log('✅ 私聊删除成功')
    } catch (error) {
      console.error('清空/删除失败:', error)
    }
  }

  /**
   * 运行完整示例
   */
  async runFullExample() {
    console.log('🚀 开始运行聊天管理系统完整示例')
    
    await this.initChatSystem()
    await this.saveGroupListFromAPI()
    await this.startPrivateChatWithUser()
    await this.sendGroupMessage()
    await this.sendPrivateMessage()
    await this.receiveWebSocketMessage()
    await this.getChatMessages()
    await this.getAllChatList()
    await this.searchMessages()
    // await this.clearAndDeleteChat() // 注释掉删除操作，避免清空数据
    
    console.log('✅ 完整示例运行完成')
  }
}

// 创建示例实例
export const chatExample = new ChatUsageExample()

// 在浏览器控制台中可以运行：
// import { chatExample } from './chatUsageExample.js'
// chatExample.runFullExample()
