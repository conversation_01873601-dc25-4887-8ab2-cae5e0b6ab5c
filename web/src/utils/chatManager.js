/**
 * 聊天管理工具类
 * 统一管理群聊和私聊的列表和记录
 */

import {
  saveGroupChatList,
  getGroupChatList,
  addPrivateChat,
  getPrivateChatList,
  getAllChatList,
  getChatMessages,
  saveChatMessage,
  updateChatListLastMessage,
  clearChatMessages,
  deleteChatListItem,
  searchChatMessages,
  initDatabase
} from './db.js'

/**
 * 聊天管理器类
 */
export class ChatManager {
  constructor() {
    this.isInitialized = false
  }

  /**
   * 初始化聊天管理器
   */
  async init() {
    try {
      console.log('初始化聊天管理器...')
      await initDatabase()
      this.isInitialized = true
      console.log('聊天管理器初始化成功')
      return true
    } catch (error) {
      console.error('聊天管理器初始化失败:', error)
      return false
    }
  }

  /**
   * 确保已初始化
   */
  async ensureInitialized() {
    if (!this.isInitialized) {
      await this.init()
    }
  }

  // ==================== 群聊管理 ====================

  /**
   * 保存群聊列表（从接口获取后调用）
   * @param {Array} groupList - 群聊列表
   */
  async saveGroupList(groupList) {
    await this.ensureInitialized()
    try {
      console.log('保存群聊列表:', groupList.length, '个群聊')
      await saveGroupChatList(groupList)
      return true
    } catch (error) {
      console.error('保存群聊列表失败:', error)
      throw error
    }
  }

  /**
   * 获取群聊列表
   */
  async getGroupList() {
    await this.ensureInitialized()
    try {
      return await getGroupChatList()
    } catch (error) {
      console.error('获取群聊列表失败:', error)
      return []
    }
  }

  /**
   * 发送群聊消息
   * @param {Object} messageData - 消息数据
   */
  async sendGroupMessage(messageData) {
    await this.ensureInitialized()
    try {
      const message = {
        ...messageData,
        typecode: 2, // 群聊类型
        timestamp: Date.now(),
        t: new Date().toISOString()
      }
      return await saveChatMessage(message, true)
    } catch (error) {
      console.error('发送群聊消息失败:', error)
      throw error
    }
  }

  /**
   * 获取群聊消息
   * @param {string} groupId - 群聊ID
   * @param {number} page - 页码
   * @param {number} size - 每页数量
   */
  async getGroupMessages(groupId, page = 1, size = 20) {
    await this.ensureInitialized()
    try {
      return await getChatMessages(groupId, true, page, size)
    } catch (error) {
      console.error('获取群聊消息失败:', error)
      return []
    }
  }

  // ==================== 私聊管理 ====================

  /**
   * 开始私聊（点击用户头像时调用）
   * @param {Object} userInfo - 用户信息
   */
  async startPrivateChat(userInfo) {
    await this.ensureInitialized()
    try {
      console.log('开始私聊:', userInfo)
      return await addPrivateChat(userInfo)
    } catch (error) {
      console.error('开始私聊失败:', error)
      throw error
    }
  }

  /**
   * 获取私聊列表
   */
  async getPrivateList() {
    await this.ensureInitialized()
    try {
      return await getPrivateChatList()
    } catch (error) {
      console.error('获取私聊列表失败:', error)
      return []
    }
  }

  /**
   * 发送私聊消息
   * @param {Object} messageData - 消息数据
   */
  async sendPrivateMessage(messageData) {
    await this.ensureInitialized()
    try {
      const message = {
        ...messageData,
        typecode: 1, // 私聊类型
        timestamp: Date.now(),
        t: new Date().toISOString()
      }
      return await saveChatMessage(message, false)
    } catch (error) {
      console.error('发送私聊消息失败:', error)
      throw error
    }
  }

  /**
   * 获取私聊消息
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} size - 每页数量
   */
  async getPrivateMessages(userId, page = 1, size = 20) {
    await this.ensureInitialized()
    try {
      return await getChatMessages(userId, false, page, size)
    } catch (error) {
      console.error('获取私聊消息失败:', error)
      return []
    }
  }

  // ==================== 通用管理 ====================

  /**
   * 获取所有聊天列表（群聊+私聊）
   */
  async getAllChats() {
    await this.ensureInitialized()
    try {
      return await getAllChatList()
    } catch (error) {
      console.error('获取所有聊天列表失败:', error)
      return []
    }
  }

  /**
   * 接收消息（WebSocket接收到消息时调用）
   * @param {Object} messageData - 消息数据
   */
  async receiveMessage(messageData) {
    await this.ensureInitialized()
    try {
      const isGroup = messageData.typecode === 2
      console.log('接收消息:', messageData, '是否群聊:', isGroup)
      
      // 保存消息到数据库
      const savedMessage = await saveChatMessage(messageData, isGroup)
      
      // 更新聊天列表的最后消息
      if (savedMessage) {
        await updateChatListLastMessage(
          messageData.chatid, 
          messageData.msg || '', 
          isGroup
        )
      }
      
      return savedMessage
    } catch (error) {
      console.error('接收消息失败:', error)
      throw error
    }
  }

  /**
   * 清空聊天记录
   * @param {string} chatId - 聊天ID
   * @param {boolean} isGroup - 是否为群聊
   */
  async clearMessages(chatId, isGroup) {
    await this.ensureInitialized()
    try {
      return await clearChatMessages(chatId, isGroup)
    } catch (error) {
      console.error('清空聊天记录失败:', error)
      throw error
    }
  }

  /**
   * 删除聊天
   * @param {string} chatId - 聊天ID
   * @param {boolean} isGroup - 是否为群聊
   */
  async deleteChat(chatId, isGroup) {
    await this.ensureInitialized()
    try {
      // 先清空聊天记录
      await clearChatMessages(chatId, isGroup)
      // 再删除聊天列表项
      await deleteChatListItem(chatId, isGroup)
      return true
    } catch (error) {
      console.error('删除聊天失败:', error)
      throw error
    }
  }

  /**
   * 搜索聊天记录
   * @param {string} keyword - 搜索关键词
   * @param {string} chatId - 聊天ID（可选）
   * @param {boolean} isGroup - 是否为群聊（可选）
   */
  async searchMessages(keyword, chatId = null, isGroup = null) {
    await this.ensureInitialized()
    try {
      return await searchChatMessages(keyword, chatId, isGroup)
    } catch (error) {
      console.error('搜索聊天记录失败:', error)
      return []
    }
  }

  /**
   * 格式化最后消息显示
   * @param {Object} message - 消息对象
   */
  formatLastMessage(message) {
    if (!message || !message.msg) return ''
    
    // 根据消息类型格式化显示
    switch (message.typecode2) {
      case 1: // 图片
        return '[图片]'
      case 2: // 语音
        return '[语音]'
      case 3: // 视频
        return '[视频]'
      case 4: // 文件
        return '[文件]'
      case 5: // 撤回消息
        return '[消息已撤回]'
      default:
        return message.msg
    }
  }

  /**
   * 格式化时间显示
   * @param {string|number} timestamp - 时间戳
   */
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date

    // 今天
    if (diff < 24 * 60 * 60 * 1000 && now.getDate() === date.getDate()) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    }
    
    // 昨天
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)
    if (yesterday.getDate() === date.getDate()) {
      return '昨天'
    }
    
    // 更早
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 创建单例实例
export const chatManager = new ChatManager()

// 导出常用方法
export {
  saveGroupChatList,
  getGroupChatList,
  addPrivateChat,
  getPrivateChatList,
  getAllChatList,
  getChatMessages,
  saveChatMessage,
  updateChatListLastMessage,
  clearChatMessages,
  deleteChatListItem,
  searchChatMessages
}
