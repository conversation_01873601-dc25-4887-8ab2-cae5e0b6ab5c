import { chatPush } from '@/api/chat'
import { encryptAESBase64 } from '@/utils/decrypt'

/**
 * 
 * @param {*} params 
 * @returns 
 * const messageParams = {
    fromid: Number,           // 发送者ID
    toId: Number,            // 接收者ID (私聊为用户ID，群聊为群组ID)
    msg: String,             // 消息内容 (已加密)
    typecode: Number,        // 消息类型：1-好友消息，2-群组消息，3-通知消息
    typecode2: Number,       // 内容类型：0-文本，1-音频，2-图片，3-视频，9-语音通话
    t: String,               // 时间戳 (ISO格式)
    groupID: Number          // 群组ID (仅群聊消息)
}
 */
export const sendMessage = async (params) => {
  const messageParams = {
    ...params,
    msg: encryptAESBase64(params.msg),
    t: new Date().toISOString()
  }
  if (params.typecode2 == 10) {
    messageParams.msg = params.msg
  }
  console.log(messageParams)
  try {
    console.log(messageParams, 'messageParams')
    const result = await chatPush(messageParams)
    console.log('++++++++++', result)
    return messageParams // 返回发送的消息对象
  } catch (error) {
    console.error('发送消息错误:', error)
    throw error // 抛出错误以便调用方处理
  }
}
