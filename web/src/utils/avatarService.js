/**
 * 用户头像管理服务
 * 负责获取、缓存和管理用户头像信息
 */

import { findImUser } from '@/api/im/imuser'
import noAvatarPng from '@/assets/noBody.png'

// 用户信息缓存
const userInfoCache = new Map()
const CACHE_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟缓存过期时间

/**
 * 获取默认头像
 * @returns {string} 默认头像URL
 */
export const getDefaultAvatar = () => {
  return noAvatarPng
}

/**
 * 处理头像URL，确保返回完整的URL
 * @param {string} avatarUrl - 头像URL
 * @returns {string} 处理后的头像URL
 */
export const processAvatarUrl = (avatarUrl) => {
  if (!avatarUrl) {
    return getDefaultAvatar()
  }
  
  // 如果已经是完整的HTTP URL，直接返回
  if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    return avatarUrl
  }
  
  // 如果是相对路径，拼接基础URL
  const baseUrl = import.meta.env.VITE_BASE_API || 'http://43.198.105.182:82'
  return `${baseUrl}${avatarUrl.startsWith('/') ? '' : '/'}${avatarUrl}`
}

/**
 * 从缓存中获取用户信息
 * @param {string|number} userId - 用户ID
 * @returns {Object|null} 用户信息或null
 */
const getUserFromCache = (userId) => {
  const cacheKey = String(userId)
  const cached = userInfoCache.get(cacheKey)
  
  if (!cached) {
    return null
  }
  
  // 检查缓存是否过期
  if (Date.now() - cached.timestamp > CACHE_EXPIRE_TIME) {
    userInfoCache.delete(cacheKey)
    return null
  }
  
  return cached.data
}

/**
 * 将用户信息存储到缓存
 * @param {string|number} userId - 用户ID
 * @param {Object} userInfo - 用户信息
 */
const setUserToCache = (userId, userInfo) => {
  const cacheKey = String(userId)
  userInfoCache.set(cacheKey, {
    data: userInfo,
    timestamp: Date.now()
  })
}

/**
 * 从API获取用户信息
 * @param {string|number} userId - 用户ID
 * @returns {Promise<Object>} 用户信息
 */
const fetchUserFromAPI = async (userId) => {
  try {
    const response = await findImUser({ id: userId })
    
    if (response.code === 0 && response.data) {
      const userInfo = {
        id: response.data.ID,
        nickname: response.data.nickName || response.data.userName || `用户${userId}`,
        avatar: processAvatarUrl(response.data.headerImg),
        originalAvatar: response.data.headerImg
      }
      
      // 缓存用户信息
      setUserToCache(userId, userInfo)
      return userInfo
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
  
  // 返回默认用户信息
  const defaultUserInfo = {
    id: userId,
    nickname: `用户${userId}`,
    avatar: getDefaultAvatar(),
    originalAvatar: ''
  }
  
  // 缓存默认信息（较短的过期时间）
  userInfoCache.set(String(userId), {
    data: defaultUserInfo,
    timestamp: Date.now() - CACHE_EXPIRE_TIME + 60000 // 1分钟后重试
  })
  
  return defaultUserInfo
}

/**
 * 获取用户信息（包含头像）
 * @param {string|number} userId - 用户ID
 * @returns {Promise<Object>} 用户信息
 */
export const getUserInfo = async (userId) => {
  if (!userId) {
    return {
      id: '',
      nickname: '未知用户',
      avatar: getDefaultAvatar(),
      originalAvatar: ''
    }
  }

  // 先从内存缓存获取
  const cached = getUserFromCache(userId)
  if (cached) {
    return cached
  }

  // 从数据库缓存获取
  try {
    const { getUserInfoFromDB } = await import('@/utils/db.js')
    const dbUserInfo = await getUserInfoFromDB(userId)
    if (dbUserInfo) {
      // 将数据库中的信息放入内存缓存
      setUserToCache(userId, dbUserInfo)
      return dbUserInfo
    }
  } catch (error) {
    console.warn('从数据库获取用户信息失败:', error)
  }

  // 从API获取并保存到数据库
  const userInfo = await fetchUserFromAPI(userId)

  // 保存到数据库
  try {
    const { saveUserInfo } = await import('@/utils/db.js')
    await saveUserInfo(userInfo)
  } catch (error) {
    console.warn('保存用户信息到数据库失败:', error)
  }

  return userInfo
}

/**
 * 批量获取用户信息
 * @param {Array<string|number>} userIds - 用户ID数组
 * @returns {Promise<Map>} 用户信息Map，key为userId，value为用户信息
 */
export const getBatchUserInfo = async (userIds) => {
  const result = new Map()
  const needFetchIds = []
  
  // 先从缓存获取
  for (const userId of userIds) {
    const cached = getUserFromCache(userId)
    if (cached) {
      result.set(String(userId), cached)
    } else {
      needFetchIds.push(userId)
    }
  }
  
  // 批量获取未缓存的用户信息
  const fetchPromises = needFetchIds.map(async (userId) => {
    const userInfo = await fetchUserFromAPI(userId)
    result.set(String(userId), userInfo)
    return userInfo
  })
  
  await Promise.all(fetchPromises)
  return result
}

/**
 * 清除用户信息缓存
 * @param {string|number} userId - 用户ID，不传则清除所有缓存
 */
export const clearUserCache = (userId = null) => {
  if (userId) {
    userInfoCache.delete(String(userId))
  } else {
    userInfoCache.clear()
  }
}

/**
 * 获取当前用户的头像信息
 * @returns {Object} 当前用户头像信息
 */
export const getCurrentUserAvatar = () => {
  try {
    // 从localStorage或sessionStorage获取当前用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || '{}')

    return {
      id: userInfo.ID || userInfo.id || '',
      nickname: userInfo.nickName || userInfo.name || '我',
      avatar: processAvatarUrl(userInfo.headerImg || userInfo.head_img),
      originalAvatar: userInfo.headerImg || userInfo.head_img || ''
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
    return {
      id: '',
      nickname: '我',
      avatar: getDefaultAvatar(),
      originalAvatar: ''
    }
  }
}

/**
 * 从群成员信息中获取用户信息
 * @param {string|number} userId - 用户ID
 * @param {string|number} groupId - 群组ID
 * @returns {Promise<Object>} 用户信息
 */
export const getUserInfoFromGroupMembers = async (userId, groupId) => {
  console.log('getUserInfoFromGroupMembers调用:', { userId, groupId })

  if (!userId) {
    return {
      id: '',
      nickname: '未知用户',
      avatar: getDefaultAvatar(),
      originalAvatar: ''
    }
  }

  try {
    // 先从内存缓存获取
    const cached = getUserFromCache(userId)
    if (cached) {
      console.log('从内存缓存获取用户信息:', cached)
      return cached
    }

    // 从数据库缓存获取
    try {
      const { getUserInfoFromDB } = await import('@/utils/db.js')
      const dbUserInfo = await getUserInfoFromDB(userId)
      if (dbUserInfo) {
        setUserToCache(userId, dbUserInfo)
        console.log('从数据库缓存获取用户信息:', dbUserInfo)
        return dbUserInfo
      }
    } catch (error) {
      console.warn('从数据库获取用户信息失败:', error)
    }

    // 如果有群组ID，尝试从群成员缓存中获取
    if (groupId) {
      try {
        console.log('尝试从群成员缓存获取用户信息:', { userId, groupId })
        const { findMemberInCache } = await import('@/utils/groupMemberUtils.js')

        const memberInfo = findMemberInCache(groupId, userId)

        if (memberInfo) {
          const userInfo = {
            id: String(userId),
            nickname: memberInfo.nickname,
            avatar: processAvatarUrl(memberInfo.avatar) || getDefaultAvatar(),
            originalAvatar: memberInfo.avatar || ''
          }

          console.log('从群成员缓存构建用户信息:', userInfo)

          // 缓存用户信息
          setUserToCache(userId, userInfo)

          // 保存到数据库
          try {
            const { saveUserInfo } = await import('@/utils/db.js')
            await saveUserInfo(userInfo)
          } catch (error) {
            console.warn('保存用户信息到数据库失败:', error)
          }

          return userInfo
        } else {
          console.log('群成员缓存中未找到用户，可能需要先加载群成员数据')
        }
      } catch (error) {
        console.warn('从群成员缓存获取用户信息失败:', error)
      }
    }

    // 最后从API获取
    console.log('从API获取用户信息:', userId)
    const userInfo = await fetchUserFromAPI(userId)

    // 保存到数据库
    try {
      const { saveUserInfo } = await import('@/utils/db.js')
      await saveUserInfo(userInfo)
    } catch (error) {
      console.warn('保存用户信息到数据库失败:', error)
    }

    return userInfo
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      id: String(userId),
      nickname: `用户${userId}`,
      avatar: getDefaultAvatar(),
      originalAvatar: ''
    }
  }
}
