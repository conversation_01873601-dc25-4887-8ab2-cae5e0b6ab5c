<template>
  <el-avatar
    :size="size"
    :src="displayAvatar"
    :class="avatarClass"
    @click="handleClick"
  >
    <el-icon><User /></el-icon>
  </el-avatar>
</template>

<script setup>
import { computed } from 'vue'
import { User } from '@element-plus/icons-vue'
import { processAvatarUrl, getDefaultAvatar } from '@/utils/avatarService.js'

defineOptions({
  name: 'ChatAvatar'
})

const props = defineProps({
  // 头像URL
  src: {
    type: String,
    default: ''
  },
  // 头像大小
  size: {
    type: [String, Number],
    default: 40
  },
  // 用户信息对象（包含avatar、senderAvatar等字段）
  userInfo: {
    type: Object,
    default: () => ({})
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['click'])

// 计算显示的头像
const displayAvatar = computed(() => {
  // 优先级：props.src > userInfo.senderAvatar > userInfo.avatar > 默认头像
  const avatarUrl = props.src || 
                   props.userInfo?.senderAvatar || 
                   props.userInfo?.avatar
  
  return processAvatarUrl(avatarUrl) || getDefaultAvatar()
})

// 计算头像样式类
const avatarClass = computed(() => {
  const classes = []
  
  if (props.clickable) {
    classes.push('chat-avatar--clickable')
  }
  
  if (props.customClass) {
    classes.push(props.customClass)
  }
  
  return classes.join(' ')
})

// 处理点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.userInfo)
  }
}
</script>

<style lang="scss" scoped>
.chat-avatar--clickable {
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}
</style>
