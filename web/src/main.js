import './style/element_visiable.scss'
import 'element-plus/theme-chalk/dark/css-vars.css'
import { createApp } from 'vue'
import ElementPlus from 'element-plus'

import 'element-plus/dist/index.css'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import { store } from '@/pinia'
import App from './App.vue'
import { initializeApp } from '@/utils/initManager.js'
// 引入调试工具（开发环境）
if (process.env.NODE_ENV === 'development') {
  import('@/utils/dbDebug.js')
  import('@/utils/debugHelper.js')
  import('@/utils/dbTest.js')
  import('@/utils/quickTest.js')
}

const app = createApp(App)
app.config.productionTip = false

// 先初始化应用和Pinia
app.use(run).use(ElementPlus).use(store).use(auth).use(router)

// 在应用挂载后初始化所有系统
app.mount('#app')

// 延迟初始化所有系统，确保应用完全启动
setTimeout(() => {
  initializeApp().then(success => {
    if (success) {
      console.log('🎉 应用系统初始化完成')
    } else {
      console.error('❌ 应用系统初始化失败')
    }
  }).catch(error => {
    console.error('❌ 应用系统初始化异常:', error)
  })
}, 500) // 延迟500ms
export default app
