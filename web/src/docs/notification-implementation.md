# 消息通知功能实现文档

## 功能概述

根据需求实现了以下两个主要功能：

### 1. 消息通知显示机制
- **别人发送的消息**：自己收到别人的消息时显示通知
- **聊天界面状态控制**：在聊天界面打开时不显示通知，聊天框关闭时才显示通知

### 2. 未读消息数量显示逻辑
- **未读数量增加**：有收到别人发的消息且聊天框关闭状态下增加未读消息数量
- **显示位置**：未读数量显示在聊天弹窗打开的图标上，不显示在WebSocket连接状态的图标上

## 实现细节

### 修改的文件

1. **web/src/utils/connectionService.js**
   - 添加了消息发送者身份识别逻辑
   - 实现了聊天界面状态管理
   - 修改了通知显示逻辑，只在聊天界面关闭时显示
   - 修改了未读数量更新逻辑

2. **web/src/view/layout/header/tools.vue**
   - 修改聊天图标，动态显示未读数量
   - 添加了WebSocket store的引用
   - 在打开聊天弹窗时清除未读数量

3. **web/src/components/WebSocketStatus.vue**
   - 移除了未读数量显示（根据需求，只在聊天图标上显示）
   - 简化了点击处理逻辑

4. **web/src/view/layout/index.vue**
   - 添加了聊天弹窗状态管理
   - 在打开/关闭聊天弹窗时更新状态

### 核心逻辑

#### 1. 消息发送者识别
```javascript
// 检查是否是自己发送的消息
const userStore = useUserStore()
const currentUserId = userStore.userInfo?.id || userStore.userInfo?.userId
const isOwnMessage = data.fromid == currentUserId
```

#### 2. 聊天界面状态管理
```javascript
// 全局状态变量
let chatDialogOpenState = false

// 设置状态函数
export const setChatDialogState = (isOpen) => {
  chatDialogOpenState = isOpen
}

// 检查状态函数
const isChatDialogOpen = () => {
  return chatDialogOpenState
}
```

#### 3. 通知显示控制
```javascript
const showMessageNotification = (messageItem, type) => {
  // 只有在聊天界面关闭时才显示通知
  if (!isChatDialogOpen()) {
    ElNotification({
      title: type,
      message: `${messageItem.senderNickname}: ${messageItem.msg}`,
      type: 'info',
      duration: 3000,
      onClick: () => {
        // 点击通知时打开聊天弹窗
        emitter.emit('openChatDialog', {
          id: messageItem.fromid,
          name: messageItem.senderNickname,
          headImg: messageItem.senderAvatar
        })
      }
    })
  }
}
```

#### 4. 未读数量管理
```javascript
const updateStoreWithMessage = (messageItem) => {
  const webSocketStore = useWebSocketStore()
  
  // 更新最新消息
  webSocketStore.lastMessage = messageItem
  
  // 只有在聊天界面关闭时才增加未读计数
  if (!isChatDialogOpen()) {
    webSocketStore.unreadCount++
  }
}
```

## 功能特点

### 1. 智能消息识别
- 自动识别消息是否为当前用户发送
- 只有别人发送的消息才会触发通知和未读计数

### 2. 状态感知通知
- 实时检测聊天界面的打开/关闭状态
- 根据界面状态决定是否显示通知

### 3. 用户体验优化
- 点击通知可直接打开聊天弹窗
- 打开聊天弹窗时自动清除未读数量
- 未读数量显示在正确的位置（聊天图标而非连接状态图标）

### 4. 数据一致性
- 消息数据正确保存到数据库
- Pinia状态与界面状态保持同步
- 未读数量准确反映实际情况

## 测试方法

可以使用提供的测试文件进行功能验证：

```javascript
import { 
  testNotificationFeatures, 
  simulateMessageReceived, 
  testChatDialogStateEffect 
} from '@/utils/notificationTest.js'

// 测试基本功能
testNotificationFeatures()

// 模拟消息接收
simulateMessageReceived()

// 测试状态影响
testChatDialogStateEffect()
```

## 注意事项

1. **用户身份识别**：确保用户信息正确设置，以便准确识别消息发送者
2. **状态同步**：聊天弹窗的打开/关闭必须正确调用状态管理函数
3. **性能考虑**：状态检查是轻量级的，不会影响性能
4. **兼容性**：实现保持了与现有代码的兼容性

## 后续扩展

如果需要进一步扩展功能，可以考虑：

1. 添加消息类型过滤（如只对某些类型的消息显示通知）
2. 实现消息优先级管理
3. 添加通知声音或震动
4. 实现更复杂的未读消息分类统计
