# WebSocket 集成文档

## 概述

本项目已成功集成WebSocket功能，实现了用户登录后自动建立WebSocket连接，支持实时消息推送、心跳保活、自动重连等功能。

## 功能特性

### 1. 自动连接管理
- ✅ 用户登录成功后自动建立WebSocket连接
- ✅ 用户登出时自动关闭WebSocket连接
- ✅ 连接断开时自动重连机制
- ✅ 心跳保活机制

### 2. 消息处理
- ✅ 支持多种消息类型处理
- ✅ 消息历史记录
- ✅ 未读消息计数
- ✅ 实时消息通知

### 3. 状态管理
- ✅ 基于Pinia的状态管理
- ✅ 连接状态实时监控
- ✅ 消息状态管理

## 技术实现

### 核心文件结构

```
web/src/
├── utils/
│   └── websocket.js              # WebSocket管理器
├── pinia/modules/
│   ├── websocket.js              # WebSocket状态管理
│   └── user.js                   # 用户状态管理（已集成WebSocket）
├── components/
│   └── WebSocketStatus.vue       # WebSocket状态组件
└── view/
    ├── websocket/
    │   └── index.vue             # WebSocket演示页面
    └── test/
        └── websocket-test.vue    # WebSocket测试页面
```

### 1. WebSocket管理器 (`utils/websocket.js`)

核心功能：
- WebSocket连接管理
- 自动重连机制
- 心跳保活
- 消息发送和接收
- 错误处理

```javascript
// 连接WebSocket
await webSocketManager.connect(token)

// 发送消息
webSocketManager.send(messageData)

// 关闭连接
webSocketManager.close()
```

### 2. 状态管理 (`pinia/modules/websocket.js`)

提供的状态和方法：
- `connectionStatus`: 连接状态
- `messageHistory`: 消息历史
- `unreadCount`: 未读消息数
- `initConnection()`: 初始化连接
- `sendMessage()`: 发送消息
- `closeConnection()`: 关闭连接

### 3. 状态组件 (`components/WebSocketStatus.vue`)

功能：
- 显示连接状态指示器
- 未读消息徽章
- 点击重连功能
- 状态提示

## 使用方法

### 1. 在组件中使用WebSocket状态

```vue
<template>
  <div>
    <!-- 显示WebSocket状态 -->
    <WebSocketStatus />
    
    <!-- 发送消息按钮 -->
    <el-button @click="sendMessage" :disabled="!isConnected">
      发送消息
    </el-button>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useWebSocketStore } from '@/pinia/modules/websocket'
import WebSocketStatus from '@/components/WebSocketStatus.vue'

const webSocketStore = useWebSocketStore()

const isConnected = computed(() => webSocketStore.isConnected)

const sendMessage = () => {
  const message = {
    type: 1,
    content: 'Hello WebSocket!',
    timestamp: Date.now()
  }
  webSocketStore.sendMessage(message)
}
</script>
```

### 2. 监听消息

```javascript
// 在组件中监听消息变化
import { watch } from 'vue'
import { useWebSocketStore } from '@/pinia/modules/websocket'

const webSocketStore = useWebSocketStore()

watch(() => webSocketStore.lastMessage, (newMessage) => {
  if (newMessage) {
    console.log('收到新消息:', newMessage)
    // 处理消息逻辑
  }
})
```

## 消息类型

系统支持以下消息类型：

| 类型 | 说明 | 处理方式 |
|------|------|----------|
| 0 | 心跳消息 | 自动处理 |
| 1 | 私聊消息 | 显示通知 |
| 2 | 群聊消息 | 显示通知 |
| 3 | 好友申请 | 显示警告通知 |
| 4 | 通过好友申请 | 显示成功消息 |
| 5 | 拒绝好友申请 | 显示警告消息 |
| 6 | 删除好友 | 显示信息消息 |
| 23 | 用户上线 | 记录日志 |
| 24 | 用户下线 | 记录日志 |

## 配置说明

### WebSocket连接地址
```
ws://**************:82/api/msgSocket?token={用户token}
```

### 重连配置
- 最大重连次数: 5次
- 重连间隔: 3秒
- 心跳间隔: 30秒

## 集成状态

### ✅ 已完成
1. WebSocket管理器实现
2. 状态管理集成
3. 用户登录/登出集成
4. 状态显示组件
5. 消息处理机制
6. 自动重连机制
7. 心跳保活机制
8. 演示和测试页面

### 🔄 可扩展功能
1. 消息加密/解密
2. 离线消息同步
3. 消息持久化存储
4. 更丰富的消息类型处理
5. 消息发送状态跟踪

## 测试验证

### 1. 登录测试
- 用户登录成功后，WebSocket应自动连接
- 在浏览器开发者工具的Network标签页可以看到WebSocket连接

### 2. 状态测试
- 查看页面右上角的WebSocket状态指示器
- 绿色圆点表示已连接
- 红色圆点表示未连接
- 黄色圆点表示连接中

### 3. 消息测试
- 可以通过测试页面发送心跳消息
- 查看消息历史记录
- 测试重连功能

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查网络连接
   - 确认服务器地址是否正确
   - 检查token是否有效

2. **自动重连不工作**
   - 检查浏览器控制台错误信息
   - 确认重连配置是否正确

3. **消息接收异常**
   - 检查消息格式是否正确
   - 确认消息处理器是否正确注册

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console标签页的日志信息
3. 查看Network标签页的WebSocket连接状态
4. 使用测试页面进行功能验证

## 总结

WebSocket功能已成功集成到项目中，实现了用户登录后自动建立连接的需求。系统具备完整的连接管理、消息处理、状态监控等功能，为实时通信提供了可靠的基础设施。
