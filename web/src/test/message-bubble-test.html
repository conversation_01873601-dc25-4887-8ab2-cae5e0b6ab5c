<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息气泡测试 - 图片消息无气泡</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .chat-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .message-item {
            display: flex;
            margin: 16px 0;
            align-items: flex-start;
            gap: 12px;
        }
        
        .message-item.own {
            flex-direction: row-reverse;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #409eff, #67c23a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .message-content {
            max-width: 60%;
            display: flex;
            flex-direction: column;
        }
        
        .message-content.own-content {
            align-items: flex-end;
        }
        
        .message-meta {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
        }
        
        .message-meta.own-meta {
            text-align: right;
        }
        
        /* 文本消息气泡 */
        .message-bubble {
            background: #f0f0f0;
            border-radius: 12px;
            padding: 12px 16px;
            position: relative;
            max-width: 100%;
            word-wrap: break-word;
        }
        
        .message-bubble.own-bubble {
            background: #409eff;
            color: white;
        }
        
        /* 图片消息无气泡样式 */
        .image-content-no-bubble {
            display: flex;
        }
        
        .image-content-no-bubble.own-image {
            justify-content: flex-end;
        }
        
        .message-image {
            width: 100px;
            height: 100px;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
            object-fit: cover;
        }
        
        .message-image:hover {
            transform: scale(1.02);
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        
        .comparison-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>消息气泡测试 - 图片消息无气泡效果</h1>
    
    <div class="test-section">
        <h2>对比效果</h2>
        <div class="comparison">
            <div class="comparison-item">
                <div class="comparison-title">❌ 修改前：图片有气泡</div>
                <div class="message-item">
                    <div class="avatar">A</div>
                    <div class="message-content">
                        <div class="message-meta">
                            <span>好友</span>
                            <span>14:30</span>
                        </div>
                        <div class="message-bubble">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzQwOWVmZiIvPgogIDx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5Zu+54mHPC90ZXh0Pgo8L3N2Zz4=" 
                                 alt="图片" class="message-image" style="background: #f0f0f0; padding: 8px;">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="comparison-item">
                <div class="comparison-title">✅ 修改后：图片无气泡</div>
                <div class="message-item">
                    <div class="avatar">A</div>
                    <div class="message-content">
                        <div class="message-meta">
                            <span>好友</span>
                            <span>14:30</span>
                        </div>
                        <div class="image-content-no-bubble">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzQwOWVmZiIvPgogIDx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5Zu+54mHPC90ZXh0Pgo8L3N2Zz4=" 
                                 alt="图片" class="message-image">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>完整聊天效果演示</h2>
        <div class="chat-container">
            <!-- 好友发送文本消息 -->
            <div class="message-item">
                <div class="avatar">A</div>
                <div class="message-content">
                    <div class="message-meta">
                        <span>Alice</span>
                        <span>14:25</span>
                    </div>
                    <div class="message-bubble">
                        你好！我发一张图片给你看看
                    </div>
                </div>
            </div>
            
            <!-- 好友发送图片消息（无气泡） -->
            <div class="message-item">
                <div class="avatar">A</div>
                <div class="message-content">
                    <div class="message-meta">
                        <span>Alice</span>
                        <span>14:26</span>
                    </div>
                    <div class="image-content-no-bubble">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzY3YzIzYSIvPgogIDx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5LuA5Lq655qE5Zu+54mHPC90ZXh0Pgo8L3N2Zz4=" 
                             alt="图片" class="message-image">
                    </div>
                </div>
            </div>
            
            <!-- 自己回复文本消息 -->
            <div class="message-item own">
                <div class="avatar">我</div>
                <div class="message-content own-content">
                    <div class="message-meta own-meta">
                        <span>14:27</span>
                    </div>
                    <div class="message-bubble own-bubble">
                        很漂亮的图片！我也发一张
                    </div>
                </div>
            </div>
            
            <!-- 自己发送图片消息（无气泡） -->
            <div class="message-item own">
                <div class="avatar">我</div>
                <div class="message-content own-content">
                    <div class="message-meta own-meta">
                        <span>14:28</span>
                    </div>
                    <div class="image-content-no-bubble own-image">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2U2YTIzYyIvPgogIDx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5oiR55qE5Zu+54mHPC90ZXh0Pgo8L3N2Zz4=" 
                             alt="图片" class="message-image">
                    </div>
                </div>
            </div>
            
            <!-- 好友回复 -->
            <div class="message-item">
                <div class="avatar">A</div>
                <div class="message-content">
                    <div class="message-meta">
                        <span>Alice</span>
                        <span>14:29</span>
                    </div>
                    <div class="message-bubble">
                        👍 很棒！
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>功能特点</h2>
        <ul>
            <li>✅ <strong>图片消息无气泡</strong>：图片直接显示，没有背景气泡包围</li>
            <li>✅ <strong>文本消息有气泡</strong>：文本消息保持原有的气泡样式</li>
            <li>✅ <strong>统一尺寸</strong>：所有图片统一显示为 100px × 100px</li>
            <li>✅ <strong>圆角效果</strong>：图片有 8px 圆角，更美观</li>
            <li>✅ <strong>阴影效果</strong>：图片有轻微阴影，增加层次感</li>
            <li>✅ <strong>悬停效果</strong>：鼠标悬停时图片轻微放大</li>
            <li>✅ <strong>对齐方式</strong>：自己发送的图片右对齐，好友发送的图片左对齐</li>
            <li>✅ <strong>预览功能</strong>：点击图片可以预览大图（需要配合 Element Plus）</li>
        </ul>
    </div>
    
    <script>
        // 添加点击图片预览功能（模拟）
        document.querySelectorAll('.message-image').forEach(img => {
            img.addEventListener('click', function() {
                alert('点击预览图片功能（在实际应用中会打开大图预览）');
            });
        });
    </script>
</body>
</html>
