<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #409eff;
        }
        .upload-area.dragover {
            border-color: #409eff;
            background-color: #f0f9ff;
        }
        .preview-image {
            max-width: 100px;
            max-height: 100px;
            border-radius: 8px;
            margin: 10px;
        }
        .message-item {
            display: flex;
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
            background-color: #f5f5f5;
        }
        .message-content {
            margin-left: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.success {
            background-color: #f0f9ff;
            color: #409eff;
        }
        .status.error {
            background-color: #fef0f0;
            color: #f56c6c;
        }
        .status.loading {
            background-color: #fdf6ec;
            color: #e6a23c;
        }
    </style>
</head>
<body>
    <h1>图片上传功能测试</h1>
    
    <div class="test-section">
        <h2>1. 文件选择测试</h2>
        <input type="file" id="fileInput" accept="image/*" multiple>
        <div id="fileInfo"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 拖拽上传测试</h2>
        <div class="upload-area" id="dropArea">
            <p>拖拽图片到这里或点击选择文件</p>
            <input type="file" id="dropFileInput" accept="image/*" style="display: none;" multiple>
        </div>
        <div id="dropFileInfo"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 图片预览测试</h2>
        <div id="previewArea"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 上传结果模拟</h2>
        <div id="uploadResults"></div>
    </div>

    <script>
        // 模拟上传文件函数
        const mockUploadFile = async (formData) => {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    const file = formData.get('file');
                    if (file.size > 10 * 1024 * 1024) {
                        reject(new Error('文件大小超过10MB'));
                        return;
                    }
                    
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                    if (!allowedTypes.includes(file.type)) {
                        reject(new Error('不支持的文件类型'));
                        return;
                    }
                    
                    // 模拟成功响应
                    resolve({
                        data: {
                            file: {
                                url: URL.createObjectURL(file),
                                name: file.name,
                                size: file.size
                            }
                        }
                    });
                }, 1000);
            });
        };

        // 文件验证函数
        const validateFile = (file) => {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            const maxSize = 10 * 1024 * 1024; // 10MB
            
            if (!allowedTypes.includes(file.type)) {
                return { valid: false, error: '只支持上传 JPG、PNG、GIF、WebP 格式的图片' };
            }
            
            if (file.size > maxSize) {
                return { valid: false, error: '图片大小不能超过 10MB' };
            }
            
            return { valid: true };
        };

        // 处理文件上传
        const handleFileUpload = async (file) => {
            const validation = validateFile(file);
            if (!validation.valid) {
                showStatus(validation.error, 'error');
                return;
            }

            showStatus('图片上传中...', 'loading');
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                const response = await mockUploadFile(formData);
                
                if (response && response.data && response.data.file) {
                    const imageUrl = response.data.file.url;
                    showStatus('图片上传成功', 'success');
                    showPreview(imageUrl, file.name);
                    simulateMessageDisplay(imageUrl);
                } else {
                    showStatus('图片上传失败，请重试', 'error');
                }
            } catch (error) {
                showStatus('图片上传失败: ' + error.message, 'error');
            }
        };

        // 显示状态
        const showStatus = (message, type) => {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            document.getElementById('uploadResults').appendChild(statusDiv);
        };

        // 显示预览
        const showPreview = (url, name) => {
            const previewDiv = document.createElement('div');
            previewDiv.innerHTML = `
                <img src="${url}" alt="${name}" class="preview-image">
                <p>文件名: ${name}</p>
                <p>预览尺寸: 100px × 100px</p>
            `;
            document.getElementById('previewArea').appendChild(previewDiv);
        };

        // 模拟消息显示
        const simulateMessageDisplay = (imageUrl) => {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-item';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <p><strong>我:</strong></p>
                    <img src="${imageUrl}" alt="发送的图片" style="width: 100px; height: 100px; border-radius: 8px;">
                    <p>图片消息 (typecode2: 2)</p>
                </div>
            `;
            document.getElementById('uploadResults').appendChild(messageDiv);
        };

        // 文件输入事件
        document.getElementById('fileInput').addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            const infoDiv = document.getElementById('fileInfo');
            infoDiv.innerHTML = '';
            
            files.forEach(file => {
                const fileDiv = document.createElement('div');
                fileDiv.innerHTML = `
                    <p>文件名: ${file.name}</p>
                    <p>大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p>类型: ${file.type}</p>
                    <button onclick="handleFileUpload(this.file)">测试上传</button>
                `;
                fileDiv.querySelector('button').file = file;
                infoDiv.appendChild(fileDiv);
            });
        });

        // 拖拽上传
        const dropArea = document.getElementById('dropArea');
        const dropFileInput = document.getElementById('dropFileInput');

        dropArea.addEventListener('click', () => {
            dropFileInput.click();
        });

        dropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropArea.classList.add('dragover');
        });

        dropArea.addEventListener('dragleave', () => {
            dropArea.classList.remove('dragover');
        });

        dropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            dropArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    handleFileUpload(file);
                }
            });
        });

        dropFileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            files.forEach(file => {
                handleFileUpload(file);
            });
        });
    </script>
</body>
</html>
