import service from '@/utils/chatRequest'

/**
 * 获取好友列表
 * @param {*} data 
 * @returns 
 */
export const getFriendList = (data) => {
  return service({
    url: `/friend/getList`,
    method: 'post',
    data
  })
}
// 群组列表
export const getGroupList = (data) => {
	return service({
    url: `/group/getList`,
    method: 'post',
    data
	})
}


// 发送消息
export const chatPush = (data) => {
    return service({
        url: `/push`,
        method: 'post',
        data
    })
}

export const getMe = (data) => {
  return service({
    url: '/user/getMe',
    method: 'post',
    data
  })
}