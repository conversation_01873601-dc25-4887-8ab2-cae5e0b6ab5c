// store/index.js
// #ifdef VUE3
import { createStore } from 'vuex'
// #endif

// #ifndef VUE3
import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
// #endif

const store = {
  state: {
    friendMsg: uni.getStorageSync('friendMsg') || [],
    groupMsg: uni.getStorageSync('groupMsg') || [],
    notifyMsg: uni.getStorageSync('notifyMsg') || [],
    chatList: uni.getStorageSync('chatList') || {},
    groupList: uni.getStorageSync('groupList') || [],
    friendList: uni.getStorageSync('friendList') || [],
	topFriendList: uni.getStorageSync('topFriendList') || [],
	config: uni.getStorageSync('config') || {}
  },
  mutations: {
	resetStore(state) {
		// 清空内存状态
		state.friendMsg = []
		state.groupMsg = []
		state.notifyMsg = []
		state.chatList = {}
		state.groupList = []
		state.friendList = []
		state.config={}
		// 同时清空本地存储
		uni.removeStorageSync('friendMsg')
		uni.removeStorageSync('groupMsg')
		uni.removeStorageSync('notifyMsg')
		uni.removeStorageSync('chatList')
		uni.removeStorageSync('groupList')
		uni.removeStorageSync('friendList')
		uni.removeStorageSync('config')
	},
	setConfig(state, payload) {
	  state.config=payload
	  uni.setStorageSync('config', state.config)
	},
    setFriendMsg(state, payload) {
      state.friendMsg.push(payload)
      uni.setStorageSync('friendMsg', state.friendMsg)
    },
    setGroupMsg(state, payload) {
      state.groupMsg.push(payload)
      uni.setStorageSync('groupMsg', state.groupMsg)
    },
    setNotifyMsg(state, payload) {
      state.notifyMsg.push(payload)
      uni.setStorageSync('notifyMsg', state.notifyMsg)
    },
    // 保存聊天列表
    setChatList(state, payload) {
      const currentTime = new Date().getTime();
      const currentChatId = uni.getStorageSync('currentChatId');
      const currentUserId = uni.getStorageSync('userId');

      if (payload.chatid in state.chatList) {
        const existingChat = state.chatList[payload.chatid];

        // 如果是当前聊天窗口，则不增加未读数
        if (currentChatId === payload.chatid) {
          payload.unreadCount = 0;
        } else {
          // 只有当消息是接收到的（不是自己发送的）时才增加未读数
          const isReceived = payload.fromid != currentUserId;
          if (isReceived) {
            payload.unreadCount = (existingChat.unreadCount || 0) + 1;
          } else {
            payload.unreadCount = existingChat.unreadCount || 0;
          }
        }

        // 保留一些现有的属性
        payload.avatar = payload.avatar || existingChat.avatar;
        payload.nickname = payload.nickname || existingChat.nickname;
        delete state.chatList[payload.chatid];  // 删除旧的记录
      } else {
        // 新聊天，如果是接收到的消息则设为1，否则为0
        const isReceived = payload.fromid != currentUserId;
        payload.unreadCount = isReceived ? 1 : 0;
      }

      // 确保必要的字段都存在
      payload.timestamp = payload.timestamp || currentTime;
      payload.lastMessage = payload.msg || payload.lastMessage || '';
      payload.avatar = payload.avatar || '/static/My/avatar.jpg';
      payload.nickname = payload.nickname || payload.phone || '未知';

      state.chatList[payload.chatid] = payload;  // 设置新的记录
      uni.setStorageSync('chatList', state.chatList)
    },

    // 更新未读计数
    updateUnreadCount(state, { chatId, count }) {
      if (state.chatList[chatId]) {
        state.chatList[chatId].unreadCount = count;
        uni.setStorageSync('chatList', state.chatList);
      }
    },

    delChatItem(state, payload) {
      const chatIdStr = String(payload.chatid);
      // 检查chatid是否存在
      console.log('检查chatid是否存在:', chatIdStr, '在', Object.keys(state.chatList));
      console.log('直接检查:', state.chatList[chatIdStr]);
      console.log('hasOwnProperty检查:', state.chatList.hasOwnProperty(chatIdStr));
      if (state.chatList.hasOwnProperty(chatIdStr)) {
        console.log('找到要删除的聊天项，开始删除...');
        // 创建新的对象，删除指定项
        const newChatList = { ...state.chatList };
        delete newChatList[chatIdStr];
        state.chatList = newChatList;
        uni.setStorageSync('chatList', state.chatList);

        console.log('删除成功，删除后的chatList:', JSON.stringify(state.chatList));
        console.log('删除后的chatList keys:', Object.keys(state.chatList));
        console.log('剩余聊天项数量:', Object.keys(state.chatList).length);
      } else {
        console.log('要删除的chatid不存在:', payload.chatid);
        console.log('转换后的chatIdStr:', chatIdStr);
        console.log('现有的chatid列表:', Object.keys(state.chatList));
        console.log('payload.chatid类型:', typeof payload.chatid);
        console.log('现有keys的类型:', Object.keys(state.chatList).map(k => typeof k));
        console.log('完整的chatList内容:', JSON.stringify(state.chatList, null, 2));
      }
    },

    // 设置群组列表
    setGroupList(state, payload) {
      state.groupList = payload;
      uni.setStorageSync('groupList', state.groupList);
    },

    // 设置好友列表
    setFriendList(state, payload) {
      state.friendList = payload;
      uni.setStorageSync('friendList', state.friendList);
    },
  },
  getters: {
    getFriendMsg(state) {
      return state.friendMsg
    },
    getGroupMsg(state) {
      return state.groupMsg
    },
    getNotifyMsg(state) {
      return state.notifyMsg
    },
    getChatList(state) {
      return Object.values(state.chatList)
    },
    getGroupList(state) {
      return state.groupList
    },
    getFriendList(state) {
      return state.friendList
    },
	getConfig(state) {
	  return state.config
	}
  }
}

// 根据环境选择合适的store创建方式
let storeInstance;

// #ifdef VUE3
storeInstance = createStore(store)
// #endif

// #ifndef VUE3
storeInstance = new Vuex.Store(store)
// #endif

export default storeInstance