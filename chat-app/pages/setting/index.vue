<template>
	<view class="profile-container">
		<!-- 状态栏占位 -->
		<view class="status_bar"></view>

		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="back-btn" @click="goBack">
				<image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
			</view>
			<view class="title">{{ $t('setting.title') }}</view>
		</view>


		<!-- 功能列表 -->
		<view class="list-area">

			<view class="list-item-wrapper" @click="showLanguageSelector">
				<view class="list-item">
					<view class="item-left">
						<text class="function-text">{{ $t('setting.language') }}</text>
					</view>
					<view class="item-right">
						<text class="current-lang">{{ currentLanguageName }}</text>
						<image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="list-item-wrapper">
				<view class="list-item">
					<view class="item-left">
						<up-icon name="bell" color="#222222" size="24"></up-icon>
						<text class="function-text">{{ $t('setting.notification') }}</text>
					</view>
					<view class="item-right">
						<image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="list-item-wrapper">
				<view class="list-item">
					<view class="item-left">
						<up-icon name="bell" color="#222222" size="24"></up-icon>
						<text class="function-text">{{ $t('setting.privacy') }}</text>
					</view>
					<view class="item-right">
						<image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="list-item-wrapper">
				<view class="list-item">
					<view class="item-left">
						<up-icon name="info-circle" color="#222222" size="23"></up-icon>
						<text class="function-text">{{ $t('setting.about') }}</text>
					</view>
					<view class="item-right">
						<image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="list-item-wrapper">
				<view class="list-item">
					<view class="item-left">
						<up-icon name="question-circle" color="#222222" size="23"></up-icon>
						<text class="function-text">{{ $t('setting.help') }}</text>
					</view>
					<view class="item-right">
						<image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="list-item-wrapper" @click="logOut">
				<view class="list-item" style="justify-content: center;">
					<text class="text-center">{{ $t('setting.logout') }}</text>
				</view>
			</view>

		</view>

		<!-- 自定义对话框 -->
		<custom-modal v-model:visible="modalConfig.visible" :content="modalConfig.content"
			:confirm-text="modalConfig.confirmText" :cancel-text="modalConfig.cancelText"
			@confirm="modalConfig.onConfirm" @cancel="modalConfig.onCancel" />

		<!-- 语言选择器 -->
		<up-action-sheet
			:show="showLangSelector"
			:actions="languageActions"
			:title="$t('setting.language')"
			@close="showLangSelector = false"
			@select="onLanguageSelect">
		</up-action-sheet>
	</view>
</template>

<script setup>
	import {
		ref,
		onUnmounted,
		getCurrentInstance,
		nextTick,
		computed
	} from 'vue';
	import {
	  closeSQL
	} from '@/utils/db.js';
	import { SetBaseUrl } from "../../api/http.js";
	import store from "../../store/index.js";
	import {
		onShow,
		onReady,
		onHide,
		onLoad
	} from "@dcloudio/uni-app";
	import CustomModal from '@/components/CustomModal.vue';

	import { t, getCurrentLanguage, getAvailableLanguages, useI18n } from '@/utils/i18n.js';
	let canvasCtx = null;
	let canvasWidth = 0;
	let canvasHeight = 0;
	let animationIntervalId = null;
	let animationFrameId = null;
	let wavePhase = 0;
	const instance = getCurrentInstance();
	let isCanvasReady = false; // 标记 Canvas 是否已成功初始化过
	let isPageVisible = true; // 标记页面是否可见

	// 用户信息
	const userInfo = ref({

	});



	// 多语言相关
	const { switchLanguage } = useI18n();
	const currentLang = ref(getCurrentLanguage());
	const showLangSelector = ref(false);

	// 当前语言显示名称
	const currentLanguageName = computed(() => {
		return t(`languages.${currentLang.value}`);
	});

	// 语言选项
	const languageActions = computed(() => {
		return getAvailableLanguages().map(lang => ({
			name: lang.name,
			value: lang.code
		}));
	});

	// 多语言方法
	const $t = (key, defaultValue = '') => {
		// 确保响应式更新
		currentLang.value; // 访问响应式变量触发更新
		return t(key, defaultValue);
	};

	// 显示语言选择器
	const showLanguageSelector = () => {
		showLangSelector.value = true;
	};

	// 选择语言
	const onLanguageSelect = (item) => {
		if (item.value !== currentLang.value) {
			// 使用 useI18n 的 switchLanguage 方法，它会自动触发全局事件
			if (switchLanguage(item.value)) {
				currentLang.value = item.value;
				/* uni.showToast({
					title: $t('common.success'),
					icon: 'success'
				}); */
			}
		}
		showLangSelector.value = false;
	};

	// 定义波浪参数
	const waves = [{
			amplitude: 25,
			frequency: 0.02,
			speed: 0.018, // 增加速度
			color: "rgba(255, 255, 255, 0.22)",
			offset: 0,
		},
		{
			amplitude: 30,
			frequency: 0.015,
			speed: 0.02, // 增加速度
			color: "rgba(255, 255, 255, 0.16)",
			offset: Math.PI / 2,
		},
		{
			amplitude: 20,
			frequency: 0.025,
			speed: 0.026, // 增加速度
			color: "rgba(255, 255, 255, 0.11)",
			offset: Math.PI,
		},
	];

	// --- 动画循环控制 ---

	// 停止所有动画循环
	const stopAnimationLoop = () => {
		// #ifdef H5
		if (animationFrameId) {
			cancelAnimationFrame(animationFrameId);
			console.log("Cancelled requestAnimationFrame:", animationFrameId);
			animationFrameId = null;
		}
		// #endif
		// #ifdef APP-PLUS || MP
		if (animationIntervalId) {
			clearInterval(animationIntervalId);
			animationIntervalId = null;
		}
		// #endif
	};

	// 启动动画循环 (根据平台选择方式)
	const startAnimationLoop = () => {
		stopAnimationLoop();

		if (!canvasCtx || !isPageVisible) {
			return;
		}

		// #ifdef H5
		animateH5();
		// #endif

		// #ifdef APP-PLUS || MP
		if (!animationIntervalId) {
			animationIntervalId = setTimeout(() => {
				if (!canvasCtx || !isPageVisible) {
					stopAnimationLoop();
					return;
				}
				updateWavePhase();
				drawWaves();
				startAnimationLoop(); // 递归调用以实现更平滑的动画
			}, 16); // 使用16ms的间隔（约60fps）
		}
		// #endif
	};

	// --- Canvas 初始化与绘制 ---

	const initCanvas = () => {
		// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || APP-PLUS || H5
		// 确保在 DOM 更新后执行
		nextTick(() => {
			const query = uni.createSelectorQuery().in(instance.proxy);
			query
				.select("#waveCanvas")
				.boundingClientRect((data) => {
					console.log("Attempting to get Canvas boundingClientRect. Data:", data);
					if (data && data.width > 0 && data.height > 0) {
						canvasWidth = data.width;
						canvasHeight = data.height;
						console.log(
							`Canvas dimensions obtained: ${canvasWidth}x${canvasHeight}`
						);

						// 确保传递 instance.proxy
						canvasCtx = uni.createCanvasContext("waveCanvas", instance.proxy);
						console.log(
							"Canvas context created:",
							canvasCtx ? "Success" : "Failed"
						);

						if (canvasCtx) {
							isCanvasReady = true; // 标记 Canvas 初始化成功
							startAnimationLoop(); // 初始化成功后启动动画
						} else {
							isCanvasReady = false;
							console.error("Failed to create canvas context.");
						}
					} else {
						isCanvasReady = false;
						console.error("无法获取 Canvas 尺寸或尺寸无效:", data);
					}
				})
				.exec();
		});
		// #endif
	};

	const drawWaves = () => {
		if (!canvasCtx) return;

		// 使用离屏绘制优化
		canvasCtx.clearRect(0, 0, canvasWidth, canvasHeight);

		// 预计算波浪点
		const points = waves.map(wave => {
			const wavePoints = [];
			for (let x = 0; x < canvasWidth; x++) {
				wavePoints.push({
					x,
					y: Math.sin(x * wave.frequency + wavePhase * wave.speed + wave.offset) * wave
						.amplitude + canvasHeight * 0.39
				});
			}
			return wavePoints;
		});

		// 绘制每个波浪
		waves.forEach((wave, index) => {
			canvasCtx.beginPath();
			canvasCtx.moveTo(0, canvasHeight);

			const wavePoints = points[index];
			for (let i = 0; i < wavePoints.length; i++) {
				canvasCtx.lineTo(wavePoints[i].x, wavePoints[i].y);
			}

			canvasCtx.lineTo(canvasWidth, canvasHeight);
			canvasCtx.closePath();
			canvasCtx.fillStyle = wave.color;
			canvasCtx.fill();
		});

		canvasCtx.draw();
	};

	const updateWavePhase = () => {
		wavePhase += 1.5; // 增加相位更新速率
	};

	// #ifdef H5
	const animateH5 = () => {
		if (!isPageVisible) return; // H5 也检查页面可见性
		updateWavePhase();
		drawWaves();
		animationFrameId = requestAnimationFrame(animateH5);
	};
	// #endif

	// --- 生命周期与页面事件 ---

	// 页面首次渲染完成时尝试初始化
	onReady(() => {
		console.log("Page Ready (onReady). Attempting initial canvas init.");
		initCanvas();
	});


	onLoad(() => {


	})
	// 页面显示/从后台返回前台时
	onShow(() => {
		userInfo.value = uni.getStorageSync("userInfo");
		isPageVisible = true; // 标记页面可见
		console.log("Page Show (onShow). Checking canvas status.");
		// 如果 Canvas 之前已就绪，尝试重启动画；否则尝试重新初始化
		if (isCanvasReady && canvasCtx) {
			console.log("Canvas was ready. Restarting animation loop.");
			startAnimationLoop();
		} else {
			console.log("Canvas not ready or context lost. Re-initializing canvas.");
			// 确保清理旧的动画和上下文引用
			stopAnimationLoop();
			canvasCtx = null;
			isCanvasReady = false;
			initCanvas(); // 尝试重新初始化
		}
	});

	// 页面隐藏/进入后台时
	onHide(() => {

		isPageVisible = false; // 标记页面不可见
		console.log("Page Hide (onHide). Stopping animation loop.");
		stopAnimationLoop(); // 停止动画节省资源
	});

	// 组件卸载时
	onUnmounted(() => {
		isPageVisible = false;
		console.log("Page Unmounted. Stopping animation loop and cleaning context.");
		stopAnimationLoop();
		canvasCtx = null; // 清理上下文引用
		isCanvasReady = false;
	});

	// 返回上一页
	const goBack = () => {
		uni.navigateBack();
	};





	// 对话框配置
	const modalConfig = ref({
		visible: false,
		content: '',
		confirmText: $t('common.confirm'),
		cancelText: $t('common.cancel'),
		onConfirm: () => {},
		onCancel: () => {}
	});


	const logOut = () => {
		uni.showModal({
			title: $t('common.confirm'),
			content: $t('setting.logoutConfirm'),
			confirmText: $t('setting.logoutBtn'),
			cancelText: $t('common.cancel'),
			success: (res) => {
				if (res.confirm) {
					uni.clearStorageSync();
					uni.closeSocket()
					SetBaseUrl('http://**************:81');
					store.commit('resetStore')
					closeSQL()
					uni.reLaunch({
						url: '/pages/login/login'
					});

				}
			}
		})
	}
</script>

<style lang="scss" scoped>
	/* 页面根元素样式 */
	page {
		height: 100vh;
		overflow: hidden;
		background-color: #f8f8f8;
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
	}

	.profile-container {
		position: relative;
		// height: 100vh;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		/* iOS 滚动橡皮筋效果禁用 */
		overscroll-behavior: none;
		-webkit-overflow-scrolling: none;
		touch-action: none;
		background: #F9F4F6;
	}

	/* 状态栏占位 */
	.status_bar {
		height: var(--status-bar-height);
		width: 100%;
		background-color: #F9F4F6;
		flex-shrink: 0;
	}

	/* 顶部导航栏 */
	.nav-bar {
		position: relative;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		z-index: 100;
		background-color: #F9F4F6;
		border-bottom: 1rpx solid #F9F4F6;

		.title {
			color: #FF3366;
		}
	}

	.back-btn {
		background-color: white;
		width: 53rpx;
		height: 53rpx;
		border-radius: 50%;
		position: absolute;
		left: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.arrow-left {
		width: 26rpx;
		height: 26rpx;
		transform: rotate(180deg);
		/* 水平翻转箭头图标 */
	}

	.title {
		font-size: 36rpx;
		font-weight: 500;
	}

	.header-section {
		position: relative;
		height: 430rpx;
		margin-bottom: 0;
		flex-shrink: 0;
		overflow: visible;
	}

	.header-background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 100%;
		background: linear-gradient(180deg, #FF3366 0%, #FF5A5F 100%);
		overflow: hidden;
		z-index: 2;
	}

	.header-background::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100%;
		/* 调整渐变，使其包含三个颜色点：透明、白色和白色 */
		background: linear-gradient(170deg,
				transparent 60%,
				/* 上部分保持透明 */
				#fff 60.5%,
				/* 开始斜线 */
				#fff 100%
				/* 底部填充白色 */
			);
		z-index: 3;
	}

	.wave-canvas {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 4;
	}

	.user-info-area {
		position: absolute;
		top: 250rpx;
		left: 40rpx;
		right: 40rpx;
		display: flex;
		align-items: flex-start;
		z-index: 10;
	}

	.avatar-container {
		position: relative;
		margin-right: 38rpx;
	}

	.avatar {
		width: 150rpx;
		height: 150rpx;
		border-radius: 50%;
		border: 4rpx solid #fff;
		background-color: #eee;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.camera-icon-wrapper {
		position: absolute;
		bottom: 12rpx;
		right: 0rpx;
		width: 44rpx;
		height: 44rpx;
		background-color: #ff5a5f;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 2;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.camera-icon {
		width: 42rpx;
		height: 42rpx;
	}

	.gender-icon-wrapper {
		position: absolute;
		bottom: 12rpx;
		right: 0rpx;
		width: 44rpx;
		height: 44rpx;
		background-color: #ff5a5f;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 2;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.gender-icon {
		font-size: 32rpx;
		color: #fff;
	}

	.text-info {
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding-top: 70rpx;

		.nickname {
			font-size: 32rpx;
			color: #252525;
			margin-bottom: 10rpx;
			font-weight: 600;
			line-height: 1;
		}

		.phone {
			font-size: 24rpx;
			color: #5C5C5C;
			font-weight: 500;
			line-height: 1;
		}
	}

	.list-area {
		margin-top: 0;
		flex: 1;
		background: #F9F4F6;
		overflow: hidden;
		position: relative;
		/* iOS 滚动橡皮筋效果禁用 */
		overscroll-behavior: none;
		-webkit-overflow-scrolling: none;
		touch-action: none;
		padding-bottom: 200rpx;
	}

	.list-item-wrapper {
		background-color: #fff;
		margin-bottom: 30rpx;
		overflow: hidden;

		// &:active {
		//   background-color: #f9f9f9;
		// }
	}

	.list-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 45rpx;

		.text-center {
			text-align: center;
		}

		.item-left {
			display: flex;
			align-items: center;
		}

		.item-right {
			display: flex;
			align-items: center;

			.current-lang {
				font-size: 28rpx;
				color: #666;
				margin-right: 10rpx;
			}

			.arrow-right {
				margin-left: 27rpx;
			}
		}
	}

	.function-text {
		font-size: 32rpx;
		color: #333;
		margin-left: 13rpx;
	}

	.danger-section {
		margin-top: 40rpx;
	}

	.danger-item {
		margin-top: 10rpx;
	}

	.danger-text {
		color: #FF3366;
		text-align: center;
	}

	/* 图标样式 */
	.iconfont {
		font-family: "iconfont";
	}

	.icon-right:after {
		content: ">";
		color: #ccc;
	}

	.icon-left:after {
		content: "<";
	}

	/* 开关样式调整 */
	switch {
		transform: scale(0.8);
	}

	/* 添加全局样式 */
	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}

	/* 右箭头图标 */
	.arrow-right {
		width: 24rpx;
		height: 24rpx;
	}

	/* 删除不再需要的图标样式 */
	/* .icon-right:after {
  content: ">";
  color: #ccc;
} */
</style>