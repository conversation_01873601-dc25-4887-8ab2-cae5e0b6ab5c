<template>
    <view class="search-container">
        <view class="status_bar"></view>
        <view class="search-header">
            <view class="search-input-wrapper">
                <view class="search-icon-wrapper">
                    <image class="search-icon" src="/static/search.webp"></image>
                </view>
                <input class="search-input" type="text" v-model="searchText" :placeholder="$t('search.placeholder')"
                    placeholder-class="placeholder" confirm-type="search" focus @input="onSearchInput"
                    @confirm="onSearchConfirm" />
                <view v-if="searchText" class="clear-icon-wrapper" @tap="clearSearch">
                    <text class="clear-icon">×</text>
                </view>
            </view>
            <view class="cancel-btn" @tap="goBack">{{ $t('search.cancel') }}</view>
        </view>

        <scroll-view class="search-content" scroll-y @scrolltolower="loadMore">
            <block v-if="searchText && contactResults.length > 0">
                <view class="search-section">
                    <view class="section-title">{{ $t('search.contacts') }}</view>
                    <view class="contact-list">
                        <view class="contact-item" v-for="(item, index) in contactResults" :key="'contact-' + index"
                            @tap="navigateToContact(item)">
                            <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
                            <view class="contact-info">
                                <view class="contact-name">{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </block>

            <block v-if="searchText && groupResults.length > 0">
                <view class="search-section">
                    <view class="section-title">{{ $t('search.groups') }}</view>
                    <view class="contact-list">
                        <view class="contact-item" v-for="(item, index) in groupResults" :key="'group-' + index"
                            @tap="navigateToGroup(item)">
                            <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
                            <view class="contact-info">
                                <view class="contact-name">{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </block>

            <block v-if="searchText && chatResults.length > 0">
                <view class="search-section">
                    <view class="section-title">{{ $t('search.chatHistory') }}</view>
                    <view class="chat-list">
                        <view class="chat-item" v-for="(item, index) in chatResults" :key="'chat-' + index"
                            @tap="navigateToChat(item)">
                            <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
                            <view class="chat-info">
                                <view class="chat-name">{{ item.name }}</view>
                                <view class="chat-message">
                                    <text class="message-text">{{ item.message }}</text>
                                </view>
                                <!-- <view class="chat-time">{{ formatTime(item.time) }}</view> -->
                            </view>
                        </view>
                    </view>
                </view>
            </block>
            <view class="no-result" v-if="searchText && searching && !hasResults">
                <text class="no-result-text">{{ $t('search.noResults') }}</text>
            </view>

            <view class="loading" v-if="isLoading">
                <view class="loading-icon"></view>
                <text class="loading-text">{{ $t('common.loading') }}</text>
            </view>
        </scroll-view>
    </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { searchChatMessages, searchChatList, getAllChatContacts, generateTestData } from '../../utils/db.js';
import { t, getCurrentLanguage } from '@/utils/i18n.js';

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

// 搜索文本
const searchText = ref('');
// 搜索状态
const searching = ref(false);
const isLoading = ref(false);

// 搜索结果
const contactResults = ref([]);
const groupResults = ref([]);
const chatResults = ref([]);

const currentPage = ref(1);
const pageSize = ref(20);
const hasMoreData = ref(true);

// 计算是否有搜索结果

const hasResults = computed(() => {
    return contactResults.value.length > 0 ||
        groupResults.value.length > 0 ||
        chatResults.value.length > 0;
});

watch(searchText, (newValue) => {
    if (newValue) {
        searching.value = true;
        setTimeout(() => {
            searchData();
        }, 300);
    } else {
        searching.value = false;
        clearResults();
    }
});

const clearSearch = () => {
    searchText.value = '';
    clearResults();
};

const clearResults = () => {
    contactResults.value = [];
    groupResults.value = [];
    chatResults.value = [];
};

// 搜索输入处理
const onSearchInput = (e) => {
};

// 搜索确认处理
const onSearchConfirm = () => {
    if (!searchText.value.trim()) return;
    searchData();
};

// 真实搜索数据
const searchData = async () => {
    if (!searchText.value.trim()) {
        clearResults();
        return;
    }

    isLoading.value = true;
    currentPage.value = 1;
    hasMoreData.value = true;

    try {
        const keyword = searchText.value.trim();
        console.log('开始搜索:', keyword);

        // 并行执行三种搜索
        const [chatListResults, chatMessageResults, contactsResults] = await Promise.all([
            // 搜索聊天列表（联系人和群组）
            Promise.resolve(searchChatList(keyword)),
            // 搜索聊天消息
            searchChatMessages(keyword, currentPage.value, pageSize.value),
            // 获取所有聊天对象用于联系人搜索
            getAllChatContacts()
        ]);

        console.log('搜索结果:', {
            chatListResults: chatListResults.length,
            chatMessageResults: chatMessageResults.length,
            contactsResults: contactsResults.length
        });

        // 处理联系人和群组结果
        const contacts = [];
        const groups = [];

        // 从聊天列表结果中分离联系人和群组
        chatListResults.forEach(item => {
            if (item.type === 'group') {
                groups.push({
                    id: item.chatId,
                    name: item.name,
                    avatar: item.avatar,
                    chatId: item.chatId
                });
            } else {
                contacts.push({
                    id: item.chatId,
                    name: item.name,
                    avatar: item.avatar,
                    chatId: item.chatId
                });
            }
        });

        // 从所有联系人中搜索匹配的
        const searchKey = keyword.toLowerCase();
        contactsResults.forEach(contact => {
            if (contact.name.toLowerCase().includes(searchKey) &&
                contact.type === 'contact' &&
                !contacts.find(c => c.id === contact.chatId)) {
                contacts.push({
                    id: contact.chatId,
                    name: contact.name,
                    avatar: contact.avatar,
                    chatId: contact.chatId
                });
            }
            if (contact.name.toLowerCase().includes(searchKey) &&
                contact.type === 'group' &&
                !groups.find(g => g.id === contact.chatId)) {
                groups.push({
                    id: contact.chatId,
                    name: contact.name,
                    avatar: contact.avatar,
                    chatId: contact.chatId
                });
            }
        });

        // 处理聊天记录结果
        const chats = [];
        const chatList = uni.getStorageSync('chatList') || {};

        chatMessageResults.forEach(msg => {
            const chatInfo = chatList[msg.chatid] || {};
            chats.push({
                id: msg.id,
                name: chatInfo.nickname || chatInfo.phone || `用户${msg.chatid}`,
                avatar: chatInfo.avatar || '/static/My/avatar.jpg',
                message: msg.msg,
                time: msg.t,
                chatId: msg.chatid,
                messageId: msg.id
            });
        });

        // 更新结果
        contactResults.value = contacts;
        groupResults.value = groups;
        chatResults.value = chats;

        // 检查是否还有更多数据
        hasMoreData.value = chatMessageResults.length >= pageSize.value;

        console.log('搜索完成:', {
            contacts: contacts.length,
            groups: groups.length,
            chats: chats.length
        });

    } catch (error) {
        console.error('搜索失败:', error);
        uni.showToast({
            title: '搜索失败，请重试',
            icon: 'none'
        });
    } finally {
        isLoading.value = false;
        searching.value = true;
    }
};

// 加载更多
const loadMore = async () => {
    if (!searchText.value || isLoading.value || !hasMoreData.value) return;

    console.log('加载更多聊天记录...');
    isLoading.value = true;

    try {
        currentPage.value += 1;
        const keyword = searchText.value.trim();

        // 只加载更多聊天消息
        const moreMessages = await searchChatMessages(keyword, currentPage.value, pageSize.value);

        if (moreMessages.length > 0) {
            const chatList = uni.getStorageSync('chatList') || {};
            const newChats = moreMessages.map(msg => {
                const chatInfo = chatList[msg.chatid] || {};
                return {
                    id: msg.id,
                    name: chatInfo.nickname || chatInfo.phone || `用户${msg.chatid}`,
                    avatar: chatInfo.avatar || '/static/My/avatar.jpg',
                    message: msg.msg,
                    time: msg.t,
                    chatId: msg.chatid,
                    messageId: msg.id
                };
            });

           
            chatResults.value = [...chatResults.value, ...newChats];

            // 检查是否还有更多数据
            hasMoreData.value = moreMessages.length >= pageSize.value;

            console.log(`加载了 ${newChats.length} 条更多消息`);
        } else {
            hasMoreData.value = false;
            console.log('没有更多消息了');
        }
    } catch (error) {
        console.error('加载更多失败:', error);
        uni.showToast({
            title: '加载失败，请重试',
            icon: 'none'
        });
    } finally {
        isLoading.value = false;
    }
};

// 联系人
const navigateToContact = (item) => {
    console.log('联系人:', item);
    uni.navigateTo({
        url: `/pages/conversation/conversation?chatid=${item.chatId}&nickname=${encodeURIComponent(item.name)}&avatar=${encodeURIComponent(item.avatar)}`
    });
};

// 群聊
const navigateToGroup = (item) => {
    console.log('群聊:', item);
    uni.navigateTo({
        url: `/pages/conversation/conversation?chatid=${item.chatId}&nickname=${encodeURIComponent(item.name)}&avatar=${encodeURIComponent(item.avatar)}&isGroup=true`
    });
};

// 聊天
const navigateToChat = (item) => {
    console.log('聊天记录:', item);
    uni.navigateTo({
        url: `/pages/conversation/conversation?chatid=${item.chatId}&nickname=${encodeURIComponent(item.name)}&avatar=${encodeURIComponent(item.avatar)}&messageId=${item.messageId}`
    });
};

// 返回
const goBack = () => {
    uni.navigateBack();
};

// 格式化时间
const formatTime = (timeStr) => {
    if (!timeStr) return '';

    try {
        const time = new Date(timeStr);
        const now = new Date();
        const diff = now.getTime() - time.getTime();

        // 一天内显示时间
        if (diff < 24 * 60 * 60 * 1000) {
            return time.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 一周内显示星期
        if (diff < 7 * 24 * 60 * 60 * 1000) {
            const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            return days[time.getDay()];
        }

        // 超过一周显示日期
        return time.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
        });
    } catch (error) {
        console.error('时间格式化失败:', error);
        return '';
    }
};
</script>

<style>
.search-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.search-header {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: #f8f8f8;
    border-bottom: 1rpx solid #eaeaea;
    height: 88rpx;
    box-sizing: border-box;
}

.search-input-wrapper {
    flex: 1;
    height: 68rpx;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 36rpx;
    padding: 0 20rpx;
}

.search-icon-wrapper {
    width: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.search-icon {
    width: 36rpx;
    height: 36rpx;
}

.search-input {
    flex: 1;
    height: 68rpx;
    font-size: 28rpx;
    margin-left: 10rpx;
}

.placeholder {
    color: #999999;
}

.clear-icon-wrapper {
    width: 50rpx;
    height: 50rpx;
    border-radius: 25rpx;
    background-color: #c8c8c8;
    display: flex;
    justify-content: center;
    align-items: center;
}

.clear-icon {
    font-size: 30rpx;
    color: #ffffff;
    font-weight: bold;
}

.cancel-btn {
    font-size: 28rpx;
    color: #ff4f81;
    padding-left: 20rpx;
}

.search-content {
    flex: 1;
    overflow-y: auto;
}

.search-hint {
    padding: 40rpx 30rpx;
    display: flex;
    justify-content: center;
}

.hint-text {
    font-size: 28rpx;
    color: #999999;
}

.search-section {
    margin-bottom: 20rpx;
}

.section-title {
    padding: 20rpx 30rpx;
    font-size: 26rpx;
    color: #999999;
    background-color: #f5f5f5;
}

.contact-list,
.chat-list {
    background-color: #ffffff;
}

.contact-item,
.chat-item {
    display: flex;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
}

.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
}

.contact-info,
.chat-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.contact-name,
.chat-name {
    font-size: 30rpx;
    color: #333333;
    margin-bottom: 8rpx;
}

.chat-message {
    font-size: 26rpx;
    color: #999999;
    margin-bottom: 8rpx;
}

.chat-time {
    font-size: 22rpx;
    color: #cccccc;
}

.message-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
}

.no-result {
    padding: 60rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.no-result-text {
    font-size: 28rpx;
    color: #999999;
}

.loading {
    padding: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-icon {
    width: 40rpx;
    height: 40rpx;
    border: 3rpx solid #f5f5f5;
    border-radius: 50%;
    border-top-color: #ff4f81;
    animation: rotate 1s linear infinite;
    margin-right: 10rpx;
}

.loading-text {
    font-size: 24rpx;
    color: #999999;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 状态栏占位 */
.status_bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: white;
    flex-shrink: 0;
}
</style>