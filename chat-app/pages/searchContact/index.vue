<template>
    <view class="search-contact">
        <view class="status_bar"></view>
        <view class="sub-box">
            <view @click="goBack">
                <up-icon name="arrow-left" color="#FD3357" size="20"></up-icon>
            </view>
            <view class="custom-subsection">
                <view
                    v-for="(item, index) in list"
                    :key="index"
			
                    class="custom-subsection-item"
                    :class="{ 'active': current === index }"
                    @click="handleChange(index)"
                >
                    {{ item.name }}
                </view>
            </view>
        </view>

        <view class="search-header">
            <view class="search-input-wrapper">
                <view class="search-icon-wrapper">
                    <u-icon name="search" color="#ABB0B6" size="20"></u-icon>
                </view>
                <input class="search-input" type="text" v-model="searchText" :placeholder="placeholder"
                    placeholder-class="placeholder" confirm-type="search"
                    @confirm="handleSearch" />
                <view v-if="searchText" class="clear-icon-wrapper" @tap="clearSearch">
                    <text class="clear-icon">×</text>
                </view>
            </view>
            <view class="cancel-btn" @tap="goBack">取消</view>
        </view>

        <view class="content">
            <view v-if="currentTab === 0" class="user-list">
                <template v-if="userList.length > 0">
                    <view v-for="user in userList" :key="user.ID" class="user-item" @click="handleUserClick(user)">
                        <u-avatar :src="user.head_img || defaultUserAvatar" size="30"></u-avatar>
                        <view class="user-info">
                            <text class="nick-desc">找人: </text>
                            <text class="nickname">{{ user.iphone_num }}</text>
                        </view>
                    </view>
                </template>
                <view v-else-if="hasSearched && userList.length === 0" class="empty-state">
                    <image :src="defaultUserAvatar" class="empty-icon"></image>
                    <text class="empty-text">没有找到相关的结果</text>
                </view>
            </view>

            <view v-if="currentTab === 1" class="group-list">
                <template v-if="groupList.length > 0">
                    <view v-for="group in groupList" :key="group.id" class="group-item"
                        @click="handleGroupClick(group)">
                        <u-avatar :src="group.avatar || defaultGroupAvatar" size="30"></u-avatar>
                        <view class="group-info">
                            <text class="nick-desc">找群: </text>
                            <text class="group-name">{{ group.name }}</text>
                        </view>
                    </view>
                </template>
                <view v-else-if="hasSearched && groupList.length === 0" class="empty-state">
                    <image :src="defaultGroupAvatar" class="empty-icon"></image>
                    <text class="empty-text">没有找到相关的结果</text>
                </view>
            </view>
        </view>

        <view v-if="isSearching" class="loading-overlay">
            <view class="loading-tip">
                <up-loading-icon vertical :text="'正在查找'+(currentTab === 0 ? '联系人' : '群聊')+'...' " color="#fff" textSize="14" size="30"></up-loading-icon>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue' // 【修改】移除了 onMounted
import { searchUser } from '@/api/friend'
import { useStore } from 'vuex';
const defaultUserAvatar = '/static/icons/official-accounts.png'
const defaultGroupAvatar = '/static/icons/tags.png'
const isSearching = ref(false)
const hasSearched = ref(false)

const list = ref([])


const placeholder = computed(() => {
    return current.value === 0 ? '搜索手机号' : '搜索群ID/群名称'
})


const current = ref(0)
const searchText = ref('')
const currentTab = ref(0)

// 用户列表
const userList = ref([])
// 群列表
const groupList = ref([])
const store = useStore();
const config = computed(() => store.getters.getConfig);

if(config.value.server?.userADDFriend){
	list.value.push({
        name: '找人',
    },)
}
if(config.value.server?.userADGroup){
	list.value.push({
        name: '找群',
    },)
}
if(list.value[0].name=='找群'){
	current.value = 1
	currentTab.value = 1
}
// 清除搜索
const clearSearch = () => {
    searchText.value = ''
    userList.value = []
    groupList.value = []
    isSearching.value = false
    hasSearched.value = false
}

// 返回上一页
const goBack = () => {
    uni.navigateBack()
}

// 处理搜索输入
const handleSearch = () => {
    if (currentTab.value == 0) {
        searchUsers()
    } else {
        searchGroups()
    }
}

// 【修改】简化了 handleChange，移除了所有DOM操作
const handleChange = (index) => {
    clearSearch()
    current.value = index
    currentTab.value = Number(index)
}

const searchUsers = async () => {
    if (!searchText.value) return
    isSearching.value = true
    try {
        const params = {
            t: 1,
            like: false,
            name: searchText.value
        }
        const {data: res} = await searchUser(params)
        hasSearched.value = true
        if(res.code==0) {
            userList.value = res.data.user
        }
    } catch (error) {
        console.error('搜索用户失败:', error)
        uni.showToast({
            title: '搜索失败，请重试',
            icon: 'none'
        })
    } finally {
        isSearching.value = false
    }
}

// 搜索群组
const searchGroups = async () => {
    if (!searchText.value) return
    isSearching.value = true
    try {
        const params = {
            t: 2,
            like: false,
            name: searchText.value
        }
        const {data: res} = await searchUser(params)
        hasSearched.value = true
        if(res.code==0) {
            groupList.value = res.data.group
        }
    } catch (error) {
        console.error('搜索群组失败:', error)
        uni.showToast({
            title: '搜索失败，请重试',
            icon: 'none'
        })
    } finally {
        isSearching.value = false
    }
}

const handleUserClick = (user) => {
    uni.navigateTo({
        url: `/pages/addFriends/index?id=${user.ID}&nickname=${user.name}&phone=${user.iphone_num}&avatar=${user.head_img || defaultUserAvatar}`
    })
}

const handleGroupClick = (group) => {
    uni.navigateTo({
        url: `/pages/group/detail?id=${group.id}`
    })
}
</script>

<style scoped>
.search-contact {
    min-height: 100vh;
    background-color: #fff;
}

.status_bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: #fff;
}

.search-header {
    display: flex;
    align-items: center;
    padding: 16rpx;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

.search-input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    height: 72rpx;
    background-color: #f7f7f7;
    border-radius: 36rpx;
    padding: 0 24rpx;
    margin-right: 20rpx;
    margin-left: 8rpx;
}

.search-icon-wrapper {
    margin-right: 16rpx;
}

.search-input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
}

.placeholder {
    color: #999;
}

.clear-icon-wrapper {
    padding: 0 10rpx;
}

.clear-icon {
    font-size: 32rpx;
    color: #999;
}

.cancel-btn {
    font-size: 26rpx;
    color: #FD3357;
    padding: 0 8rpx;
}

.content {
    /* padding: 20rpx; */
}

.user-item,
.group-item {
    display: flex;
    align-items: center;
    padding: 24rpx;
    margin-bottom: 20rpx;
}

.user-info,
.group-info {
    margin-left: 10rpx;
    flex: 1;
}
.nick-desc{
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
}

.nickname,
.group-name {
    font-size: 26rpx;
    color: #FD3357;
    font-weight: 500;
}

.member-count {
    font-size: 26rpx;
    color: #999;
    margin-top: 8rpx;
}

.loading-tip {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 12rpx;
    padding: 30rpx 40rpx;
    min-width: 240rpx;
}

.loading-tip text {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #FFFFFF;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;
}

.empty-icon {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 32rpx;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
}

.sub-box {
    background-color: #fff;
    padding: 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.custom-subsection {
    width: 40%;
    margin: 0 auto;
    height: 60rpx;
    display: flex;
    border-radius: 8rpx;
    overflow: hidden;
    border: 1px solid #FD3357;
}

.custom-subsection-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    color: #FD3357;
    font-size: 28rpx;
    transition: all 0.2s ease-in-out;
}

.custom-subsection-item.active {
    background-color: #FD3357;
    color: #FFFFFF;
}


/* 【修改】清理了不再需要的或通用的 ::v-deep 样式 */
::v-deep .u-loading-icon__text{
    width: 160rpx !important;
    text-align: center !important;
    margin-top: 10rpx !important;
}
::v-deep .loading-tip{
    padding: 10rpx 0rpx !important;
    height: 230rpx !important;
}
::v-deep .uicon-arrow-left{
    font-weight: 600 !important;
}
</style>