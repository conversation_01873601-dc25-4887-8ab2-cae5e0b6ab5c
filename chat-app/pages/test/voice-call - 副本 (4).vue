<template>
  <view class="container">
    <view class="header">
      <text class="title">WebRTC语音通话</text>
      <text class="status">状态: {{ connectionStatus }}</text>
    </view>

    <view class="info-box" v-if="localId">
      <text>我的ID: {{ localId }}</text>
      <text v-if="remoteId">对方ID: {{ remoteId }}</text>
      <text v-if="roomId">房间号: {{ roomId }}</text>
    </view>

    <!-- 呼叫面板 -->
    <view class="panel" v-if="showRequestPanel && !isInCall">
      <view v-if="!hasSentRequest">
        <input 
          v-model="targetUserId" 
          placeholder="输入对方ID" 
          class="input"
        />
        <button @click="sendCallRequest" class="btn call-btn">发起通话</button>
      </view>
      <view v-else class="waiting">
        <text>等待对方响应...</text>
        <button @click="cancelRequest" class="btn cancel-btn">取消</button>
      </view>
    </view>

    <!-- 来电显示 -->
    <view class="incoming-call" v-if="incomingRequest && !isInCall">
      <text>{{ incomingRequest.from }} 请求通话</text>
      <view class="call-buttons">
        <button @click="acceptCall" class="btn accept-btn">接听</button>
        <button @click="rejectCall" class="btn reject-btn">拒绝</button>
      </view>
    </view>

    <!-- 通话中界面 -->
    <view class="call-container" v-if="isInCall">
      <!-- 音频控制 -->
      <view class="audio-controls">
        <button @click="toggleMute" :class="['btn', isMuted ? 'btn-danger' : 'btn-success']">
          {{ isMuted ? '取消静音' : '静音' }}
        </button>
        <button @click="toggleSpeaker" :class="['btn', isSpeakerOn ? 'btn-success' : 'btn-secondary']">
          {{ isSpeakerOn ? '扬声器开' : '扬声器关' }}
        </button>
      </view>

      <!-- 消息列表 -->
      <scroll-view class="message-list" scroll-y>
        <view v-for="(msg, index) in messages" :key="index" class="message">
          <text class="time">[{{ msg.timestamp }}]</text>
          <text class="sender">{{ msg.sender }}:</text>
          <text class="content">{{ msg.content }}</text>
        </view>
      </scroll-view>

      <!-- 消息输入 -->
      <view class="input-area">
        <input
          v-model="newMessage"
          @confirm="sendMessage"
          placeholder="输入消息..."
          :disabled="!isDataChannelReady"
          class="message-input"
        />
        <button @click="sendMessage" :disabled="!isDataChannelReady" class="btn send-btn">发送</button>
        <button @click="endCall" class="btn end-call-btn">结束通话</button>
      </view>
    </view>

    <!-- 隐藏的音频元素 -->
    <!-- <uni-audio ref="remoteAudio" autoplay playsinline></uni-audio>
    <uni-audio ref="localAudio" muted playsinline></uni-audio> -->
  </view>
</template>

<script>
import { decryptAESBase64, encryptAESBase64 } from '@/utils/decrypt.js'

export default {
  data() {
    return {
      connectionStatus: '未连接',
      localId: null,
      remoteId: null,
      roomId: null,
      ws: null,
      peerConnection: null,
      dataChannel: null,
      messages: [],
      newMessage: '',
      pendingMessages: [],
      isInCall: false,
      showRequestPanel: true,
      hasSentRequest: false,
      incomingRequest: null,
      targetUserId: '',
      callRequestTimer: null,
      
      // 音频相关
      localStream: null,
      remoteStream: null,
      isMuted: false,
      isSpeakerOn: true,
      audioInputDevices: [],
      selectedAudioDevice: '',
	  remoteAudio: null,
	  localAudio: null,
	  ringtone: null
    }
  },
  computed: {
    isDataChannelReady() {
		console.log('this.dataChannel && this.dataChannel.readyState === "open"',this.dataChannel )
      return this.dataChannel && this.dataChannel.readyState === 'open'
    }
  },
  mounted() {
    this.localId = this.$route.query.userId || `user_${Math.random().toString(36).substr(2, 8)}`
    this.initAudioContexts()
	this.initConnection()
    
    // 检测设备兼容性
    this.checkCompatibility()
  },
  beforeUnmount() {
    this.cleanup()
  },
  methods: {
	  initAudioContexts() {
		  // Remote audio (plays other person's voice)
		  this.remoteAudio = uni.createInnerAudioContext()
		  this.remoteAudio.obeyMuteSwitch = false // iOS specific - allow playing when mute switch is on
		  
		  // Local audio (for echo cancellation testing, usually not needed)
		  this.localAudio = uni.createInnerAudioContext()
		  
		  // Ringtone for incoming calls
		  this.ringtone = uni.createInnerAudioContext()
		  this.ringtone.src = '/static/ringtone.mp3' // Add your ringtone file
		  this.ringtone.loop = true

		
		// ... other methods ...
	  },
    // 检查设备兼容性
    checkCompatibility() {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        uni.showModal({
          title: '不兼容提示',
          content: '您的设备不支持语音通话功能',
          showCancel: false
        })
      }
    },

    // 初始化连接
    initConnection() {
      this.connectionStatus = '正在连接信令服务器...'
      this.initWebSocket()
    },

    // WebSocket初始化
    initWebSocket() {
      this.ws = new WebSocket(`ws://43.198.105.182:82/api/msgSocket?token=${this.$route.query.token}`)

      this.ws.onopen = () => {
        this.connectionStatus = '信令服务器已连接'
        console.log('WebSocket连接成功')
      }

      this.ws.onmessage = async (event) => {
        try {
			console.log(event.data)
          const message = JSON.parse(event.data)
          if (message.msg) {
            console.log('解密前:', message.msg)
            // console.log('解密后:', decryptAESBase64(message.msg))
          }
          await this.handleServerMessage(message)
        } catch (error) {
          console.error('消息处理错误:', error)
        }
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        this.connectionStatus = '连接错误'
      }

      this.ws.onclose = () => {
        if (this.connectionStatus !== '已断开连接') {
          this.connectionStatus = '连接已断开'
          // 尝试重新连接
          setTimeout(() => this.initWebSocket(), 3000)
        }
      }
    },

    // 处理服务器消息
    async handleServerMessage(message) {
      switch (message.typecode2) {
        case 9: // 通话请求
          this.handleIncomingCall(message)
          break
        case 10: // 通话响应
          this.handleCallResponse(message)
          break
        case 11: // 房间分配
          await this.handleRoomAssignment(message)
          break
        case 12: // 来电通知
          this.handleIncomingCall(message)
          break
        case 13: // 通话拒绝
          this.handleCallRejected(message)
          break
        default:
          console.warn('未知消息类型:', message)
      }
    },

    // 发送通话请求
    sendCallRequest() {
      if (!this.targetUserId) {
        uni.showToast({ title: '请输入对方ID', icon: 'none' })
        return
      }

      this.hasSentRequest = true
      this.remoteId = this.targetUserId

      this.sendToServer({
        typecode: 1,
        typecode2: 9,
        fromid: Number(this.localId),
        toid: Number(this.targetUserId),
        msg: null
      })

      // 设置30秒超时
      this.callRequestTimer = setTimeout(() => {
        if (!this.isInCall) {
          uni.showToast({ title: '请求超时', icon: 'none' })
          this.hasSentRequest = false
          this.connectionStatus = '请求超时'
        }
      }, 30000)
    },

    // 取消请求
    cancelRequest() {
      this.hasSentRequest = false
      clearTimeout(this.callRequestTimer)
      this.connectionStatus = '已取消请求'
    },

    // 处理来电
    handleIncomingCall(message) {
      // 如果已经在通话中，自动拒绝新来电
      if (this.isInCall) {
        this.sendToServer({
          typecode: 1,
          typecode2: 13,
          fromid: Number(this.localId),
          toid: Number(message.fromid),
          msg: null
        })
        return
      }

      this.incomingRequest = {
        from: message.fromid,
        data: message
      }
      this.remoteId = message.fromid
      this.showRequestPanel = false

      // 播放铃声
      this.playRingtone()
    },

    // 播放铃声
    playRingtone() {
      // 实际项目中可以添加铃声文件
	  if (this.ringtone) {
	      this.ringtone.play()
	    }
      console.log('播放来电铃声')
    },

    // 停止铃声
    stopRingtone() {
      console.log('停止铃声')
	  if (this.ringtone) {
	      this.ringtone.stop()
	    }
    },

    // 接听来电
    async acceptCall() {
      this.stopRingtone()
      this.isInCall = true
      this.incomingRequest = null
      
      this.sendToServer({
        typecode: 1,
        typecode2: 10,
        fromid: Number(this.localId),
        toid: Number(this.remoteId),
		// 0是接通  1是拒绝
		 msg: JSON.stringify({ apply: 0 })
        // msg: encryptAESBase64(JSON.stringify({ apply: 0 }))
      })
    },

    // 拒绝来电
    rejectCall() {
      this.stopRingtone()
      this.sendToServer({
        typecode: 1,
        typecode2: 13,
        fromid: Number(this.localId),
        toid: Number(this.remoteId),
        msg: null
      })
      this.resetCallState()
    },

    // 处理被拒绝
    handleCallRejected() {
      uni.showToast({ title: '对方拒绝了通话', icon: 'none' })
      this.hasSentRequest = false
      clearTimeout(this.callRequestTimer)
      this.resetCallState()
    },
	async handleWebRTCMessage(message) {
	  console.log('收到WebRTC消息:', message.type, message)
	  
	  try {
	    switch (message.type) {
	      case 'offer':
	        // 确保PeerConnection已初始化
	        if (!this.peerConnection) {
	          await this.initWebRTC()
	        }
	        await this.handleOffer(message.data)
	        break
	        
	      case 'answer':
	        await this.handleAnswer(message.data)
	        break
	        
	      case 'candidate':
	        await this.handleCandidate(message.data)
	        break
	        
	      default:
	        console.warn('未知的WebRTC消息类型:', message.type)
	    }
	  } catch (error) {
	    console.error('处理WebRTC消息失败:', error)
	    this.connectionStatus = '消息处理错误'
	    
	    // 重要：处理失败时尝试恢复
	    if (error.toString().includes('InvalidStateError')) {
	      setTimeout(() => this.handleWebRTCMessage(message), 500)
	    }
	  }
	},
    // 处理房间分配
    async handleRoomAssignment(message) {
      try {
        // message.msg = JSON.parse(decryptAESBase64(message.msg))
		message.msg = JSON.parse(message.msg)
        this.roomId = message.msg.room
        this.connectionStatus = `已加入房间: ${this.roomId}`

        if (message.msg.roomMsg) {
          await this.handleWebRTCMessage(message.msg.roomMsg)
        } else {
          await this.initWebRTC()
        }
      } catch (error) {
        console.error('房间处理错误:', error)
      }
    },

    // 初始化WebRTC连接
    async initWebRTC() {
      this.isInCall = true
      this.showRequestPanel = false
		
      const configuration = {
        iceServers: [
          {
            urls: [
              'stun:43.198.105.182:82',
              'turn:43.198.105.182:82?transport=udp'
            ],
            username: 'your_username',
            credential: 'your_username'
          }
        ],
        iceTransportPolicy: 'all'
      }

      try {
        // 1. 创建连接
        this.peerConnection = new RTCPeerConnection(configuration)

        // 2. 设置ICE候选处理
        this.peerConnection.onicecandidate = (event) => {
          if (event.candidate) {
            this.sendWebRTCMessage({
              type: 'candidate',
              data: event.candidate
            })
          }
        }

        // 3. 设置连接状态监听
        this.peerConnection.onconnectionstatechange = () => {
          console.log('连接状态:', this.peerConnection.connectionState)
          if (this.peerConnection.connectionState === 'disconnected') {
            this.handleDisconnect()
          }
        }

        // 4. 获取麦克风权限并添加轨道
        await this.setupAudio()

        // 5. 设置数据通道
        this.setupDataChannels()

        // 6. 创建Offer（如果是发起方）
        if (this.hasSentRequest) {
          await this.createOffer()
        }else{
			
		}

      } catch (error) {
        console.error('WebRTC初始化失败:', error)
        uni.showToast({ title: '连接建立失败', icon: 'none' })
        this.cleanup()
      }
    },

    // 设置音频
    async setupAudio() {
      try {
        // 获取麦克风权限
        this.localStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            channelCount: 1
          },
          video: false
        })

        // 添加音频轨道
        this.localStream.getAudioTracks().forEach(track => {
          this.peerConnection.addTrack(track, this.localStream)
        })

        // 设置远程流监听
        this.peerConnection.ontrack = (event) => {
			if (!this.remoteStream) {
				this.remoteStream = new MediaStream()
			}
			event.streams[0].getAudioTracks().forEach(track => {
				this.remoteStream.addTrack(track)
				  

			// 	try {
			// 		const audioContext = uni.createInnerAudioContext()
		  
			// 		if (track && typeof track.getAudioTracks === 'function') {
			
			// 		  console.log('Audio track received')
			// 		}
			// 	} catch (e) {
			// 		console.error('Error handling audio track:', e)
			// 	}
			})
			// 处理远程音频播放
			this.setupRemoteAudioPlayback();
        }
		
        // 获取音频设备列表
        await this.updateAudioDevices()

      } catch (error) {
        console.error('音频设置失败:', error)
        throw error
      }
    },
	setupRemoteAudioPlayback() {
	  // 停止之前的音频
	  if (this.remoteAudio) {
	    this.remoteAudio.stop();
	    this.remoteAudio.destroy();
	  }
	
	  // 创建新的音频上下文
	  this.remoteAudio = uni.createInnerAudioContext();
	  this.remoteAudio.obeyMuteSwitch = false; // iOS忽略静音开关
	  
	  // 平台特定处理
	  if (typeof window !== 'undefined' && window.AudioContext) {
	      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
	      const mediaStreamSource = audioContext.createMediaStreamSource(this.remoteStream);
	      const destination = audioContext.createMediaStreamDestination();
	      
	      mediaStreamSource.connect(destination);
	      
	      // 创建一个虚拟音频元素
	      const audioElement = new Audio();
	      audioElement.srcObject = destination.stream;
	      audioElement.play().catch(e => {
	        console.error('自动播放失败:', e);
	        // 可能需要用户交互才能播放
	        uni.showToast({
	          title: '请点击页面以允许音频播放',
	          icon: 'none'
	        });
	      });
	      
	      // 存储引用以便清理
	      this.webAudioElements = {
	        audioContext,
	        mediaStreamSource,
	        audioElement
	      };
	    } else {
	      console.warn('Web Audio API not supported');
	      uni.showToast({
	        title: '浏览器不支持Web Audio API',
	        icon: 'none'
	      });
	    }
	  
	  // 通用设置
	  this.remoteAudio.onError((err) => {
	    console.error('远程音频播放错误:', err);
	    // 尝试重新初始化
	    setTimeout(() => this.setupRemoteAudioPlayback(), 1000);
	  });
	},

    // 更新音频设备列表
    async updateAudioDevices() {
      const devices = await navigator.mediaDevices.enumerateDevices()
      this.audioInputDevices = devices.filter(d => d.kind === 'audioinput')
      if (this.audioInputDevices.length > 0) {
        this.selectedAudioDevice = this.audioInputDevices[0].deviceId
      }
    },

    // 切换音频设备
    async changeAudioDevice() {
      try {
        const constraints = {
          audio: { 
            deviceId: { exact: this.selectedAudioDevice },
            echoCancellation: true,
            noiseSuppression: true 
          },
          video: false
        }
        
        const newStream = await navigator.mediaDevices.getUserMedia(constraints)
        const newTrack = newStream.getAudioTracks()[0]
        
        // 替换轨道
        const sender = this.peerConnection.getSenders()
          .find(s => s.track && s.track.kind === 'audio')
        
        if (sender) {
          await sender.replaceTrack(newTrack)
          // 停止旧轨道并更新本地流
          this.localStream.getAudioTracks().forEach(t => t.stop())
          this.localStream.removeTrack(this.localStream.getAudioTracks()[0])
          this.localStream.addTrack(newTrack)
        }
        
      } catch (error) {
        console.error('切换设备失败:', error)
        uni.showToast({ title: '麦克风切换失败', icon: 'none' })
      }
    },

    // 静音切换
    toggleMute() {
      this.isMuted = !this.isMuted
      if (this.localStream) {
        this.localStream.getAudioTracks().forEach(track => {
          track.enabled = !this.isMuted
        })
      }
      uni.showToast({
        title: this.isMuted ? '已静音' : '已取消静音',
        icon: 'none'
      })
    },

    // 扬声器切换
    toggleSpeaker() {
      this.isSpeakerOn = !this.isSpeakerOn
        if (this.remoteAudio) {
          // On some platforms, we need to destroy and recreate the audio context
          this.remoteAudio = uni.createInnerAudioContext()
          if (this.remoteStream) {
            // You'll need to handle the stream differently for uni-app
            // This might require platform-specific handling
          }
        }
    },

    // 创建Offer
    async createOffer() {
      try {
        const offer = await this.peerConnection.createOffer({
          offerToReceiveAudio: true,
          offerToReceiveVideo: false
        })
        
        await this.peerConnection.setLocalDescription(offer)
        
        this.sendWebRTCMessage({
          type: 'offer',
          data: offer
        })
        
      } catch (error) {
        console.error('创建Offer失败:', error)
        throw error
      }
    },

    // 处理Offer
    async handleOffer(offer) {
      try {
		  if (!this.peerConnection) {
		      await this.initWebRTCMode()
		    }
       await this.peerConnection.setRemoteDescription(
           new RTCSessionDescription(offer)
         )
        
        const answer = await this.peerConnection.createAnswer()
        await this.peerConnection.setLocalDescription(answer)
        
        this.sendWebRTCMessage({
          type: 'answer',
          data: answer
        })
        
      } catch (error) {
        console.error('处理Offer失败:', error)
        throw error
      }
    },

    // 处理Answer
    async handleAnswer(answer) {
      try {
        // 检查状态
        if (this.peerConnection.signalingState !== 'have-local-offer') {
          console.warn('非预期状态:', this.peerConnection.signalingState)
          setTimeout(() => this.handleAnswer(answer), 500)
          return
        }
        
        await this.peerConnection.setRemoteDescription(answer)
        console.log('Answer设置成功')
        
      } catch (error) {
        console.error('处理Answer失败:', error)
        throw error
      }
    },

    // 处理ICE候选
    async handleCandidate(candidate) {
      try {
        // 确保远程描述已设置
        if (!this.peerConnection.remoteDescription) {
          setTimeout(() => this.handleCandidate(candidate), 100)
          return
        }
        
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate))
        
      } catch (error) {
        console.error('添加候选失败:', error)
      }
    },

    // 设置数据通道
    setupDataChannels() {
      // 创建数据通道（如果是发起方）
      if (this.hasSentRequest) {
        this.dataChannel = this.peerConnection.createDataChannel('chat', {
          ordered: true,
          maxPacketLifeTime: 3000
        })
        
        this.setupDataChannelEvents()
      }
      
      // 监听数据通道（如果是接收方）
      this.peerConnection.ondatachannel = (event) => {
        this.dataChannel = event.channel
        this.setupDataChannelEvents()
      }
    },

    // 设置数据通道事件
    setupDataChannelEvents() {
      this.dataChannel.onopen = () => {
        console.log('数据通道已打开')
        this.connectionStatus = '通话已连接'
        this.flushPendingMessages()
      }
      
      this.dataChannel.onclose = () => {
        console.log('数据通道已关闭')
        this.handleDisconnect()
      }
      
      this.dataChannel.onmessage = (event) => {
        this.addMessage(event.data, this.remoteId)
      }
    },

    // 发送消息
    sendMessage() {
      const message = this.newMessage.trim()
      if (!message) return

      if (this.isDataChannelReady) {
        try {
          this.dataChannel.send(message)
          this.addMessage(message, '我')
          this.newMessage = ''
        } catch (error) {
          console.error('发送失败:', error)
        }
      } else {
        this.pendingMessages.push(message)
      }
    },

    // 添加消息到聊天
    addMessage(content, sender) {
      this.messages.push({
        content,
        sender,
        timestamp: new Date().toLocaleTimeString()
      })
      
      // 滚动到底部
      this.$nextTick(() => {
        const list = this.$refs.messageList
        if (list) {
          list.scrollTop = list.scrollHeight
        }
      })
    },

    // 发送WebRTC消息
    sendWebRTCMessage(message) {
      this.sendToServer({
        typecode: 1,
        typecode2: 11,
        fromid: Number(this.localId),
        toid: Number(this.remoteId),
		msg:JSON.stringify({
          room: this.roomId,
          roomMsg: message
        })
        // msg: encryptAESBase64(JSON.stringify({
        //   room: this.roomId,
        //   roomMsg: message
        // }))
      })
    },

    // 发送普通消息
    sendToServer(message) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message))
      }
    },

    // 处理断开连接
    handleDisconnect() {
      uni.showToast({ title: '连接已断开', icon: 'none' })
      this.cleanup()
      this.resetCallState()
    },

    // 结束通话
    endCall() {
      this.sendToServer({
        typecode: 1,
        typecode2: 13,
        fromid: Number(this.localId),
        toid: Number(this.remoteId),
        msg: null
      })
      
      this.cleanup()
      this.resetCallState()
      uni.showToast({ title: '通话已结束', icon: 'success' })
    },

    // 清理资源
    cleanup() {
      // 清理媒体流
      if (this.remoteAudio) {
          this.remoteAudio.destroy()
          this.remoteAudio = null
        }
        
        if (this.localAudio) {
          this.localAudio.destroy()
          this.localAudio = null
        }
        
        if (this.ringtone) {
          this.ringtone.stop()
          this.ringtone.destroy()
          this.ringtone = null
        }
      
      // 清理数据通道
      if (this.dataChannel) {
        this.dataChannel.close()
        this.dataChannel = null
      }
      
      // 清理定时器
      clearTimeout(this.callRequestTimer)
      
      // 重置音频元素
      if (this.$refs.remoteAudio) {
        this.$refs.remoteAudio.srcObject = null
      }
      if (this.$refs.localAudio) {
        this.$refs.localAudio.srcObject = null
      }
    },

    // 重置通话状态
    resetCallState() {
      this.isInCall = false
      this.hasSentRequest = false
      this.incomingRequest = null
      this.showRequestPanel = true
      this.connectionStatus = '准备就绪'
      this.roomId = null
      this.remoteId = null
      this.pendingMessages = []
    },

    // 发送待处理消息
    flushPendingMessages() {
      while (this.pendingMessages.length > 0 && this.isDataChannelReady) {
        const msg = this.pendingMessages.shift()
        this.dataChannel.send(msg)
        this.addMessage(msg, '我')
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  max-width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  margin-bottom: 20px;
  text-align: center;
}

.title {
  font-size: 24px;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.status {
  color: #666;
  font-size: 14px;
}

.info-box {
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-box text {
  display: block;
  margin-bottom: 5px;
}

.panel, .incoming-call {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.btn {
  padding: 10px 15px;
  border-radius: 4px;
  border: none;
  color: white;
  font-weight: bold;
  margin-right: 10px;
  margin-bottom: 10px;
}

.call-btn {
  background-color: #07c160;
}

.cancel-btn {
  background-color: #ff976a;
}

.accept-btn {
  background-color: #07c160;
}

.reject-btn {
  background-color: #ee0a24;
}

.send-btn {
  background-color: #1989fa;
}

.end-call-btn {
  background-color: #ee0a24;
}

.btn-success {
  background-color: #07c160;
}

.btn-danger {
  background-color: #ee0a24;
}

.btn-secondary {
  background-color: #ff976a;
}

.waiting {
  text-align: center;
}

.call-buttons {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.call-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.audio-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 15px;
  gap: 10px;
}

.message-list {
  flex: 1;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow-y: auto;
  max-height: 300px;
}

.message {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #e6f7ff;
  border-radius: 4px;
}

.time {
  color: #999;
  font-size: 12px;
  margin-right: 5px;
}

.sender {
  font-weight: bold;
  margin-right: 5px;
}

.input-area {
  display: flex;
  gap: 10px;
}

.message-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

audio {
  display: none;
}
</style>