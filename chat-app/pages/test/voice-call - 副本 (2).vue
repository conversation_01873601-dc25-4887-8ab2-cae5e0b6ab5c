<template>
  <div class="container">
    <h1>WebRTC Chat</h1>
    <div class="status">
      <span>连接状态: {{ connectionStatus }}</span><br>
      <span v-if="localId">我的ID: {{ localId }}</span><br>
      <span v-if="remoteId">对方ID: {{ remoteId }}</span><br>
      <span v-if="roomId">房间号: {{ roomId }}</span>
    </div>

    <div v-if="showRequestPanel && !isInCall">
      <div v-if="!hasSentRequest">
        <input v-model="targetUserId" placeholder="输入对方ID">
        <button @click="sendCallRequest">发起通话请求</button>
      </div>
      <div v-else>
        <p>等待对方响应...</p>
        <button @click="cancelRequest">取消请求</button>
      </div>
    </div>

    <div v-if="incomingRequest && !isInCall">
      <p>{{ incomingRequest.from }} 请求与您通话</p>
      <button @click="acceptCall">接受</button>
      <button @click="rejectCall">拒绝</button>
    </div>

    <div class="message-list" v-if="isInCall">
      <div v-for="(msg, index) in messages" :key="index" class="message">
        [{{ msg.timestamp }}] {{ msg.sender }}: {{ msg.content }}
      </div>
    </div>

    <div class="input-area" v-if="isInCall">
      <input
          v-model="newMessage"
          @keyup.enter="sendMessage"
          placeholder="输入消息..."
          :disabled="!isDataChannelReady"
      />
      <button @click="sendMessage" :disabled="!isDataChannelReady">发送</button>
      <button @click="endCall" class="end-call">结束通话</button>
    </div>
  </div>
</template>

<script>
	import { decryptAESBase64, encryptAESBase64 } from '@/utils/decrypt.js'
export default {
  data() {
    return {
      connectionStatus: '未连接',
      localId: null,
      remoteId: null,
      roomId: null,
      ws: null,
      peerConnection: null,
      dataChannel: null,
      messages: [],
      newMessage: '',
      pendingMessages: [],
      isInCall: false,
      showRequestPanel: true,
      hasSentRequest: false,
      incomingRequest: null,
      targetUserId: '',
      callRequestTimer: null
    };
  },
  computed: {
    isDataChannelReady() {
      return this.dataChannel && this.dataChannel.readyState === 'open';
    }
  },
  mounted() {
    // 从路由参数获取用户ID
    this.localId = this.$route.query.userId || Math.random().toString(36).substr(2, 8);
    this.initConnection();
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    initConnection() {
      this.connectionStatus = '正在连接信令服务器...';
      this.initWebSocket();
    },

    initWebSocket() {
      this.ws = new WebSocket(`ws://43.198.105.182:82/api/msgSocket?token=${this.$route.query.token}`);

      this.ws.onopen = () => {
        this.connectionStatus = '信令服务器已连接';
      };

      this.ws.onmessage = async (event) => {
        try {
			console.log('处理服务器消息:', event);
          const message = JSON.parse(event.data);
		  // console.log(message.msg)
		  if(message.msg){
			  console.log(decryptAESBase64(message.msg))
		  }
		  
          await this.handleServerMessage(message);
        } catch (error) {
          console.error('处理服务器消息错误:', error);
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
      };

      this.ws.onclose = () => {
        if (this.connectionStatus !== '已断开连接') {
          this.connectionStatus = '连接断开';
        }
      };
    },

    async handleServerMessage(message) {
      switch (message.typecode2) {
		case 9: // 通话请求响应
			this.handleIncomingCall(message);
			break;
		    
        case 10: // 通话请求响应
          this.handleCallResponse(message);
          break;
          
        case 11: // 房间分配
          this.handleRoomAssignment(message);
          break;
          
        case 12: // 通话请求
          this.handleIncomingCall(message);
          break;
          
        case 13: // 通话拒绝
          this.handleCallRejected(message);
          break;
          
        default:
          console.warn('未知消息类型:', message.typecode2);
      }
    },

    // 发送通话请求
    sendCallRequest() {
      if (!this.targetUserId) return;
      
      this.hasSentRequest = true;
      this.remoteId = this.targetUserId;
      
      this.sendToServer({
        typecode: 1,
        typecode2: 9,
        fromid: this.localId*1,
        toid: this.targetUserId*1,
        msg: null
      });
      
      // 设置超时
      this.callRequestTimer = setTimeout(() => {
        if (!this.isInCall) {
          this.connectionStatus = '请求超时';
          this.hasSentRequest = false;
        }
      }, 30000);
    },

    // 取消通话请求
    cancelRequest() {
      this.hasSentRequest = false;
      clearTimeout(this.callRequestTimer);
      this.connectionStatus = '已取消请求';
    },
	handleCallResponse1(message){
		console.log(this.incomingRequest,this.incomingRequestisInCall)
		
	},
    // 处理通话请求响应
    handleCallResponse(message) {
      if (message.msg.apply !== 0) {
        // 对方直接同意，等待服务器分配房间
        this.connectionStatus = '对方已同意，等待建立连接...';
      } else {
        // 需要进一步协商
        this.connectionStatus = '对方正在考虑您的请求...';
      }
    },

    // 处理房间分配
    async handleRoomAssignment(message) {
		// message=JSON.parse(message)
		
		message.msg=JSON.parse(decryptAESBase64(message.msg))
		console.log('message处理房间分配',message)
      this.roomId = message.msg.room;
      this.connectionStatus = `已加入房间: ${this.roomId}`;
      
      if (message.msg.roomMsg) {
        // 处理房间内的WebRTC消息
        await this.handleWebRTCMessage(message.msg.roomMsg);
      } else {
        // 初始化WebRTC连接
        await this.initWebRTC();
      }
    },

    // 处理来电
    handleIncomingCall(message) {
		console.log(message)
      this.incomingRequest = {
        from: message.fromid,
        data: message
      };
      this.remoteId = message.fromid;
	  console.log( this.remoteId)
      this.showRequestPanel = false;
    },

    // 接受通话
    acceptCall() {
      this.isInCall = true;
      this.incomingRequest = null;
      console.log( this.remoteId)
      this.sendToServer({
        typecode: 1,
        typecode2: 10,
        fromid: this.localId*1,
        toid: this.remoteId*1,
        msg: encryptAESBase64(JSON.stringify({ apply: 0 })) // 直接同意
      });
    },

    // 拒绝通话
    rejectCall() {
      this.sendToServer({
        typecode: 1,
        typecode2: 13,
        fromid: this.localId*1,
        toid: this.remoteId*1,
        msg: null
      });
      
      this.incomingRequest = null;
      this.showRequestPanel = true;
      this.connectionStatus = '已拒绝通话请求';
    },

    // 处理被拒绝的情况
    handleCallRejected(message) {
      this.hasSentRequest = false;
      clearTimeout(this.callRequestTimer);
      this.connectionStatus = '对方拒绝了您的通话请求';
    },

    // 初始化WebRTC连接
    async initWebRTC() {
      this.isInCall = true;
      this.showRequestPanel = false;
      
      const configuration = {
        iceServers: [
          {
            urls: [
              'turn:43.198.105.182:82?transport=udp',
              'stun:43.198.105.182:82'
            ],
            username: 'your_username',
            credential: 'your_username'
          }
        ]
      };
	  /* const configuration = {
	    iceServers: [
	      // 优先使用你的 TURN 服务器（UDP）
	      { urls: "stun:stun.qq.com:3478" },      // 腾讯STUN
	          { 
	            urls: "turn:turn.bistri.com:80",      // 国际服务（可能高延迟）
	            username: "homeo",
	            credential: "homeo"
	          }
	    ],
	    // 可选：强制使用中继（测试时可用）
	    iceTransportPolicy: 'relay' // 或 'relay'（仅限中继）
	  }; */
      try {
        this.peerConnection = new RTCPeerConnection(configuration);

        // ICE 候选处理
        this.peerConnection.onicecandidate = (event) => {
          if (event.candidate) {
            this.sendWebRTCMessage({
              type: 'candidate',
              data: event.candidate
            });
          }
        };

        // 数据通道
        this.setupDataChannels();

        // 创建Offer
        if (this.hasSentRequest) { // 发起方创建Offer
          await this.createOffer();
        }

      } catch (error) {
        console.error('初始化WebRTC失败:', error);
        this.connectionStatus = 'WebRTC初始化失败';
      }
    },

    // 设置数据通道
    setupDataChannels() {
      this.dataChannel = this.peerConnection.createDataChannel('chat', {
        ordered: true
      });

      this.dataChannel.onopen = () => {
        this.connectionStatus = 'P2P连接已建立';
        this.flushPendingMessages();
      };

      this.dataChannel.onmessage = (event) => {
        this.addMessage(event.data, this.remoteId);
      };

      // 处理远端数据通道
      this.peerConnection.ondatachannel = (event) => {
        event.channel.onmessage = (e) => {
          this.addMessage(e.data, this.remoteId);
        };
      };
    },

    // 创建Offer
    async createOffer() {
      try {
        const offer = await this.peerConnection.createOffer();
        await this.peerConnection.setLocalDescription(offer);
        
        this.sendWebRTCMessage({
          type: 'offer',
          data: offer
        });
      } catch (error) {
        console.error('创建Offer失败:', error);
      }
    },

    // 处理WebRTC消息
    async handleWebRTCMessage(message) {
		console.log('当前 signalingState:', this.peerConnection?.signalingState);
		  console.log('收到WebRTC消息:', message);
      try {
        switch (message.type) {
          case 'offer':
            await this.handleOffer(message.data);
            break;
            
          case 'answer':
            await this.handleAnswer(message.data);
            break;
            
          case 'candidate':
            await this.handleCandidate(message.data);
            break;
        }
      } catch (error) {
        console.error('处理WebRTC消息失败:', error);
      }
    },

    // 处理Offer
    async handleOffer(offer) {
		console.log('处理offer')
      await this.peerConnection.setRemoteDescription(offer);
      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);
      
      this.sendWebRTCMessage({
        type: 'answer',
        data: answer
      });
    },

    // 处理Answer
    async handleAnswer(answer) {
		console.log('处理answer')

        if (!this.peerConnection) {
            console.error('PeerConnection 未初始化');
            return;
          }
          console.log('当前状态:', this.peerConnection.signalingState);
          if (this.peerConnection.signalingState !== 'have-local-offer') {
            console.warn('状态不匹配，延迟处理answer...');
            setTimeout(() => this.handleAnswer(answer), 500);
            return;
          }
          try {
            await this.peerConnection.setRemoteDescription(answer);
            console.log('answer设置成功');
          } catch (error) {
            console.error('设置answer失败:', error);
          }
    },

    // 处理Candidate
    async handleCandidate(candidate) {
      if (!this.peerConnection.remoteDescription) {
          console.log('延迟处理候选，等待远程描述...');
          setTimeout(() => this.handleCandidate(candidate), 100);
          return;
        }
        try {
          await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        } catch (e) {
          console.error('添加ICE候选失败:', e);
        }
    },

    // 发送消息
    sendMessage() {
      const message = this.newMessage.trim();
      if (!message) return;

      if (this.isDataChannelReady) {
        this.dataChannel.send(message);
        this.addMessage(message, '我');
        this.newMessage = '';
      } else {
        this.pendingMessages.push(message);
      }
    },

    // 发送WebRTC消息（通过服务器转发）
    sendWebRTCMessage(message) {
      this.sendToServer({
        typecode: 1,
        typecode2: 11,
        fromid: this.localId*1,
        toid: this.remoteId*1,
        msg:encryptAESBase64(JSON.stringify({
          room: this.roomId,
          roomMsg: message
        }) )
      });
    },

    // 发送普通服务器消息
    sendToServer(message) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message));
      }
    },

    // 结束通话
    endCall() {
      this.cleanup();
      this.isInCall = false;
      this.showRequestPanel = true;
      this.hasSentRequest = false;
      this.connectionStatus = '通话已结束';
    },

    // 添加消息到界面
    addMessage(content, sender) {
      this.messages.push({
        content,
        sender,
        timestamp: new Date().toLocaleTimeString()
      });
    },

    // 发送待处理消息
    flushPendingMessages() {
      while (this.pendingMessages.length > 0 && this.isDataChannelReady) {
        const msg = this.pendingMessages.shift();
        this.dataChannel.send(msg);
        this.addMessage(msg, '我');
      }
    },

    // 清理资源
    cleanup() {
      if (this.peerConnection) {
        this.peerConnection.close();
        this.peerConnection = null;
      }
      
      if (this.dataChannel) {
        this.dataChannel.close();
        this.dataChannel = null;
      }
      
      clearTimeout(this.callRequestTimer);
    }
  }
};
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.status {
  margin: 10px 0;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 4px;
}

.message-list {
  height: 400px;
  border: 1px solid #ccc;
  margin: 10px 0;
  padding: 10px;
  overflow-y: auto;
}

.message {
  margin: 5px 0;
  padding: 5px;
  background: #f8f8f8;
  border-radius: 3px;
}

.input-area {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.end-call {
  background: #dc3545;
}

.request-panel {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}
</style>