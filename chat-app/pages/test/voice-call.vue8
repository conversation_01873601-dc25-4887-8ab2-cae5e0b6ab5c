<template>
	<view class="container">
		<view class="header">
			<text class="title">WebRTC语音通话</text>
			<text class="status">状态: {{ connectionStatus }}</text>
		</view>

		<view class="info-box" v-if="localId">
			<text>我的ID: {{ localId }}</text>
			<text v-if="remoteId">对方ID: {{ remoteId }}</text>
			<text v-if="roomId">房间号: {{ roomId }}</text>
		</view>

		<!-- 呼叫面板 -->
		<view class="panel" v-if="showRequestPanel && !isInCall">
			<view v-if="!hasSentRequest">
				<input v-model="targetUserId" placeholder="输入对方ID" class="input" />
				<button @click="sendCallRequest" class="btn call-btn">发起通话</button>
			</view>
			<view v-else class="waiting">
				<text>等待对方响应...</text>
				<button @click="cancelRequest" class="btn cancel-btn">取消</button>
			</view>
		</view>

		<!-- 来电显示 -->
		<view class="incoming-call" v-if="incomingRequest && !isInCall">
			<text>{{ incomingRequest.from }} 请求通话</text>
			<view class="call-buttons">
				<button @click="acceptCall" class="btn accept-btn">接听</button>
				<button @click="rejectCall" class="btn reject-btn">拒绝</button>
			</view>
		</view>

		<!-- 通话中界面 -->
		<view class="call-container" v-if="isInCall">
			<!-- 音频控制 -->
			<view class="audio-controls">
				<button @click="toggleMute" :class="['btn', isMuted ? 'btn-danger' : 'btn-success']">
					{{ isMuted ? '取消静音' : '静音' }}
				</button>
				<button @click="toggleSpeaker" :class="['btn', isSpeakerOn ? 'btn-success' : 'btn-secondary']">
					{{ isSpeakerOn ? '扬声器开' : '扬声器关' }}
				</button>
			</view>

			<!-- 消息列表 -->
			<scroll-view class="message-list" scroll-y>
				<view v-for="(msg, index) in messages" :key="index" class="message">
					<text class="time">[{{ msg.timestamp }}]</text>
					<text class="sender">{{ msg.sender }}:</text>
					<text class="content">{{ msg.content }}</text>
				</view>
			</scroll-view>

			<!-- 消息输入 -->
			<view class="input-area">
				<input v-model="newMessage" @confirm="sendMessage" placeholder="输入消息..." :disabled="!isDataChannelReady"
					class="message-input" />
				<button @click="sendMessage" :disabled="!isDataChannelReady" class="btn send-btn">发送</button>
				<button @click="endCall" class="btn end-call-btn">结束通话</button>
			</view>
		</view>

		<!-- 隐藏的音频元素 -->
		<!-- <uni-audio ref="remoteAudio" autoplay playsinline></uni-audio>
    <uni-audio ref="localAudio" muted playsinline></uni-audio> -->

	</view>
</template>

<script>
	import {
		decryptAESBase64,
		encryptAESBase64
	} from '@/utils/decrypt.js'
	// const nimCallKit = uni.requireNativePlugin('netease-CallKit'))
	import permision from "@/NERtcUniappSDK-JS/permission.js";
	import NERTC from "@/NERtcUniappSDK-JS/lib/index";
	// import NertcLocalView from "@/NERtcUniappSDK-JS/nertc-view/NertcLocalView";
	// import NertcRemoteView from "@/NERtcUniappSDK-JS/nertc-view/NertcRemoteView";
	import {
		NERTCRenderMode,
		NERTCChannelConnectionState,
		NERTCMirrorMode,
		NERtcVideoStreamType,
		NERtcVideoFrameRate,
		NERtcVideoCropMode,
		NERtcDegradationPreference,
		NERtcVideoOutputOrientationMode,
		NERtcVideoProfileType,
		NERtcRemoteVideoStreamType,
		NERTCAudioDevice,
		NERTCAudioDeviceType,
		NERTCAudioDeviceState,
		NERTCVideoDeviceState,
		NERTCConnectionType,
		NERTCErrorCode,
		NERtcAudioVolumeInfo,
		NERTCAudioProfile,
		NERTCAudioScenario,
		NERTCChannelProfile,
		NERTCUserRole,
		NERtcSubStreamContentPrefer
	} from '@/NERtcUniappSDK-JS/lib/NERtcDefines';
	export default {
		// components: {
		// 	NertcRemoteView: NertcRemoteView,
		// 	NertcLocalView: NertcLocalView
		// },
		data() {
			return {
				connectionStatus: '未连接',
				localId: null,
				remoteId: null,
				roomId: null,
				ws: null,
				peerConnection: null,
				dataChannel: null,
				messages: [],
				newMessage: '',
				pendingMessages: [],
				isInCall: false,
				showRequestPanel: true,
				hasSentRequest: false,
				incomingRequest: null,
				targetUserId: '',
				callRequestTimer: null,

				// 音频相关
				localStream: null,
				remoteStream: null,
				isMuted: false,
				isSpeakerOn: true,
				audioInputDevices: [],
				selectedAudioDevice: '',
				remoteAudio: null,
				localAudio: null,
				ringtone: null,
				engine: null,
				remoteUserIdVideoList: [],
			}
		},
		watch:{
			remoteUserIdVideoList:{
				handler(newV){
					console.log('用户列表',newV)
				},
				deep:true
			}
		},
		computed: {
			isDataChannelReady() {
				console.log('this.dataChannel && this.dataChannel.readyState === "open"', this.dataChannel)
				return this.dataChannel && this.dataChannel.readyState === 'open'
			}
		},
		onLoad(options) {
			console.log('options', options)
			this.localId = options.userId
			this.createEngine()
			this.token = uni.getStorageSync("token")
			// this.initAudioContexts()
			uni.$on('chat-message-received',async (message) => {
				console.log('收到新消息:', message);
				await this.handleServerMessage(message)
			})
			

			// 检测设备兼容性
			// this.checkCompatibility()
		},
		mounted() {
			// this.localId = this.$route.query.userId || `user_${Math.random().toString(36).substr(2, 8)}`
			
		},
		beforeUnmount() {

		},
		methods: {
			appendActionInfo(m){
				console.log(m)
			},
			// 处理服务器消息
			async handleServerMessage(message) {
				switch (message.typecode2) {
					case 9: // 通话请求
						this.handleIncomingCall(message)
						break
					case 10: // 通话响应
						this.handleCallResponse(message)
						break
					case 11: // 房间分配
						await this.handleRoomAssignment(message)
						break

					case 13: // 通话拒绝
						this.handleCallRejected(message)
						break
					default:
						console.warn('未知消息类型:', message)
				}
			},
			
			handleIncomingCall(message) {
				console.log(message)
			  this.incomingRequest = {
				from: message.fromid,
				data: message
			  };
			  this.remoteId = message.fromid;
			  console.log( this.remoteId)
			  this.showRequestPanel = false;
			},
			// 发送通话请求
			sendCallRequest() {
	
				if (!this.targetUserId) {
					uni.showToast({
						title: '请输入对方ID',
						icon: 'none'
					})
					return
				}

				this.hasSentRequest = true
				this.remoteId = this.targetUserId

				this.sendToServer({
					typecode: 1,
					typecode2: 9,
					fromid: Number(this.localId),
					toid: Number(this.targetUserId),
					msg: null
				})


			},




			// 接听来电
			async acceptCall() {

				this.isInCall = true
				this.incomingRequest = null

				this.sendToServer({
					typecode: 1,
					typecode2: 10,
					fromid: Number(this.localId),
					toid: Number(this.remoteId),
					// 0是接通  1是拒绝
					msg: JSON.stringify({
						apply: 0
					})
					// msg: encryptAESBase64(JSON.stringify({ apply: 0 }))
				})
			},



			// 处理房间分配
			async handleRoomAssignment(message) {
				try {
					// message.msg = JSON.parse(decryptAESBase64(message.msg))
					message.msg = JSON.parse(message.msg)
					this.roomId = message.msg.room
					console.log('获取token----', message)
					console.log(message.msg.token, this.roomId, this.localId * 1)
					this.engine.joinChannel({
						token: message.msg.token,
						channelName: this.roomId, //自定义房间名称
						myUid: this.localId * 1 //该房间中个人userID标识，要求number类型（不要超出number范围），房间中唯一
					});
					// this.joinChannel({
					// 	token: message.msg.token, //如果云信业务后台开启了安全模式，需要填写token
					// 	channelName: this.roomId, //自定义房间名称
					// 	myUid: this.localId*1 //该房间中个人userID自定义标识，要求number类型（不要超出number范围），房间中唯一
					// });

				} catch (error) {
					console.error('房间处理错误:', error)
				}
			},



			// 发送普通消息
			sendToServer(message) {

				console.log('发送消息messagemessagemessagemessage', message)
				uni.sendSocketMessage({
					data: JSON.stringify(message)
				})

			},

			createEngine() {
				console.log('初始化NERTC引擎');
				this.engine = NERTC.setupEngineWithContext({
					appKey: '65e8972096018e57b568c4bd5e714ac8', // your appkey
					logDir: '', // expected log directory
					logLevel: 3
				});
				this.engine.setChannelProfile(NERTCChannelProfile.COMMUNICATION);
				this.engine.enableLocalVideo({
					enable: false, //true表示设置启动摄像头，false表示关闭摄像头
					videoStreamType: 0 //0表示视频，1表示屏幕共享
				})
				console.log('初始化引擎完成，开始设置本地视频画布');
				// this.engine.setupLocalVideoCanvas({
				// 	renderMode: 0, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 
				// 	mirrorMode: 1, //1表示启用镜像，2表示不启用
				// 	isMediaOverlay: false //表示小画布置于大画布上面（从5.3.8002版本开始，该参数已废弃）
				// })

				// this.appendActionInfo('初始化引擎完成，启动视频')
				// this.engine.enableLocalVideo({
				// 	enable: false, //true表示设置启动摄像头，false表示关闭摄像头
				// 	videoStreamType: 0 //0表示视频，1表示屏幕共享 //当前demo先使用数字
				// })
				// this.engine.enableLocalAudio(true) 
				//判断权限
				if (uni.getSystemInfoSync().platform === "android") {
					permision.requestAndroidPermission(
						"android.permission.RECORD_AUDIO"
					);
					// permision.requestAndroidPermission(
					// 	"android.permission.CAMERA"
					// );
				}

				//注册NERTC的事件
				this.engine.addEventListener("onError", (code, message, extraInfo) => {
					message = `onError通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
					console.log(message)
				});

				this.engine.addEventListener("onWaring", (code, message, extraInfo) => {
					message = `onWaring通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
					console.log(message)
				});

				// //重要：UI视频view渲染的时间点不确定，有可能会出现设置videoCanvas时，对应的视频view还没有渲染好，导致设置videoCanvas失败，用户需要监听该事件去重新设置对应的videoCanvas
				// this.engine.addEventListener("onVideoCanvas", (direction, videoStreamType, userID) => {
				// 	const imessage =
				// 		`onVideoCanvas通知：direction = ${direction}, videoStreamType = ${videoStreamType}, userID = ${userID}`
				// 	console.log(imessage)
				// 	if (direction == 'local') {
				// 		if (videoStreamType == 'video') {
				// 			//重新设置本地Video（即摄像头）的画布
				// 			this.engine.setupLocalVideoCanvas({
				// 				renderMode: 0, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 
				// 				mirrorMode: 1, //1表示启用镜像，2表示不启用
				// 				isMediaOverlay: false //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 			})
				// 		} else if (videoStreamType == 'subStreamVideo') {
				// 			//重新设置本地subStramVideo(即屏幕共享)的画布
				// 			this.engine.setupLocalSubStreamVideoCanvas({
				// 				renderMode: 0, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 
				// 				mirrorMode: 1, //1表示启用镜像，2表示不启用
				// 				isMediaOverlay: false //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 			})
				// 		}

				// 	} else if (direction == 'remote') {
				// 		if (videoStreamType == 'video') {
				// 			//重新设置远端Video（即摄像头）的画布
				// 			this.engine.setupRemoteVideoCanvas({
				// 				renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
				// 				mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
				// 				isMediaOverlay: false, //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 				userID,
				// 			})
				// 		} else if (videoStreamType == 'subStreamVideo') {
				// 			//重新设置远端subStramVideo(即屏幕共享)的画布
				// 			this.engine.setupRemoteSubStreamVideoCanvas({
				// 				renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
				// 				mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
				// 				isMediaOverlay: false, //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 				userID,
				// 			})
				// 		}
				// 	}
				// });

				this.engine.addEventListener("onJoinChannel", (result, channelId, elapsed, userID) => {
					const message =
						`onJoinChannel通知：自己加入房间状况，result = ${result}, channelId = ${channelId}, elapsed = ${elapsed}, userID = ${userID}`
					console.log(message)
				});

				this.engine.addEventListener("onLeaveChannel", (result) => {
					const message = `onLeaveChannel通知：自己离开房间状况，result = ${result}`
					console.log(message)
				});

				this.engine.addEventListener("onUserJoined", (userID) => {
					const message = `onUserJoined通知：有人加入房间，userID = ${userID}`
					console.log(message)
				});

				this.engine.addEventListener("onUserLeave", (userID, reason) => {
					const message = `onUserLeave通知：有人离开房间，userID = ${userID}, reason = ${reason}`
					console.log(message)
				});

				this.engine.addEventListener("onUserAudioStart", (userID) => {
					const message = `onUserAudioStart通知：对方开启音频，userID = ${userID}`
					console.log(message)
				});

				this.engine.addEventListener("onUserAudioStop", (userID) => {
					const message = `onUserAudioStop通知：对方关闭音频，userID = ${userID}`
					console.log(message)
				});

				// this.engine.addEventListener("onUserVideoStart", (userID, maxProfile) => {
				// 	const message = `onUserVideoStart通知：对方开启视频，userID = ${userID}, maxProfile = ${maxProfile}`
				// 	console.log(message)

				// 	//nertc-remote-view组件 viewID和userID的数据格式是String类型，onUserVideoStart事件通知的userID是number，这里做一下数据格式转换
				// 	const remoteUserID = `${userID}`
				// 	if (!this.remoteUserIdVideoList.includes(remoteUserID)) {
				// 		this.remoteUserIdVideoList.push(remoteUserID);
				// 		//保证当前nertc-remote-view组件渲染完成，在执行设置画布的接口
				// 		this.$nextTick(() => {
				// 			console.log('此时远端视频 nertc-remote-view 渲染完成')
				// 			//需要开发者主动去做订阅对方视频的逻辑动作
				// 			this.subscribeRemoteVideo(userID)
				// 		})
				// 	}
				// });

				// this.engine.addEventListener("onUserVideoStop", (userID) => {
				// 	const message = `onUserVideoStop通知：对方关闭视频，userID = ${userID}`
				// 	console.log(message)
				// });

			},

			destroyEngine() {
				console.log('销毁NERTC引擎')
				//清除注册的所有事件
				this.engine?.removeAllEventListener()
				//释放资源
				this.engine?.destroyEngine()
				this.engine = null
				this.remoteUserIdVideoList = []
			},

			joinChannel(token, roomid, userid) {
				console.log('加入房间')
				this.engine.joinChannel({
					token: token,
					channelName: roomid, //自定义房间名称
					myUid: userid //该房间中个人userID标识，要求number类型（不要超出number范围），房间中唯一
				});
			},

			leaveChannel() {
				console.log('离开房间')
				this.engine.leaveChannel();
				this.remoteUserIdVideoList = []
			},

			subscribeRemoteVideo(remoteUserID) {
				console.log(`开始拉流: ${remoteUserID}, 首先设置对端视频画布`)
				this.engine.setupRemoteVideoCanvas({
					renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
					mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
					isMediaOverlay: false, //表示小画布置于大画布上面（从5.3.8002版本开始，该参数已废弃）
					userID: remoteUserID,
				});

				console.log(`开始拉流: ${remoteUserID}, 然后订阅对端视频`)
				this.engine.subscribeRemoteVideo({
					userID: remoteUserID,
					streamType: 0, //0表示大流，1表示小流
					subscribe: true //true表示订阅，false表示取消订阅
				})
			},





		}
	}
</script>

<style scoped>
	.container {
		padding: 20px;
		max-width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.header {
		margin-bottom: 20px;
		text-align: center;
	}

	.title {
		font-size: 24px;
		font-weight: bold;
		display: block;
		margin-bottom: 5px;
	}

	.status {
		color: #666;
		font-size: 14px;
	}

	.info-box {
		background-color: #fff;
		padding: 15px;
		border-radius: 8px;
		margin-bottom: 20px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.info-box text {
		display: block;
		margin-bottom: 5px;
	}

	.panel,
	.incoming-call {
		background-color: #fff;
		padding: 20px;
		border-radius: 8px;
		margin-bottom: 20px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.input {
		width: 100%;
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
		margin-bottom: 10px;
	}

	.btn {
		padding: 10px 15px;
		border-radius: 4px;
		border: none;
		color: white;
		font-weight: bold;
		margin-right: 10px;
		margin-bottom: 10px;
	}

	.call-btn {
		background-color: #07c160;
	}

	.cancel-btn {
		background-color: #ff976a;
	}

	.accept-btn {
		background-color: #07c160;
	}

	.reject-btn {
		background-color: #ee0a24;
	}

	.send-btn {
		background-color: #1989fa;
	}

	.end-call-btn {
		background-color: #ee0a24;
	}

	.btn-success {
		background-color: #07c160;
	}

	.btn-danger {
		background-color: #ee0a24;
	}

	.btn-secondary {
		background-color: #ff976a;
	}

	.waiting {
		text-align: center;
	}

	.call-buttons {
		display: flex;
		justify-content: center;
		margin-top: 15px;
	}

	.call-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		background-color: #fff;
		border-radius: 8px;
		padding: 15px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.audio-controls {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		margin-bottom: 15px;
		gap: 10px;
	}

	.message-list {
		flex: 1;
		margin-bottom: 15px;
		padding: 10px;
		background-color: #f9f9f9;
		border-radius: 4px;
		overflow-y: auto;
		max-height: 300px;
	}

	.message {
		margin-bottom: 10px;
		padding: 8px;
		background-color: #e6f7ff;
		border-radius: 4px;
	}

	.time {
		color: #999;
		font-size: 12px;
		margin-right: 5px;
	}

	.sender {
		font-weight: bold;
		margin-right: 5px;
	}

	.input-area {
		display: flex;
		gap: 10px;
	}

	.message-input {
		flex: 1;
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
	}

	audio {
		display: none;
	}
</style>