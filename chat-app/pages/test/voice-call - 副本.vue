<template>
  <view class="voice-call-container">
    <!-- 通话状态显示 -->
    <view class="call-status">
      <text>{{ callStatus }}</text>
      <text v-if="duration">{{ formatDuration(duration) }}</text>
    </view>
    
    <!-- 对方信息 -->
    <view class="remote-user" v-if="remoteUserInfo">
      <image :src="remoteUserInfo.avatar" class="avatar"></image>
      <text class="name">{{ remoteUserInfo.name }}</text>
    </view>
    
    <!-- 控制按钮 -->
    <view class="control-buttons">
      <button 
        class="talk-button"
        @touchstart="startTalking"
        @touchend="stopTalking"
        :disabled="!isConnected"
      >
        {{ isTalking ? '松开结束' : '按住说话' }}
      </button>
      
      <button 
        class="mute-button"
        @click="toggleMute"
        :class="{ active: isMuted }"
      >
        {{ isMuted ? '取消静音' : '静音' }}
      </button>
      
      <button 
        class="end-call-button"
        @click="endCall"
      >
        结束通话
      </button>
    </view>
    
    <!-- 音频元素 -->
    <uni-audio :srcObject.prop="remoteStream" autoplay></uni-audio>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { WebRTCManager } from '@/utils/webrtc.js';

// 路由参数
const routeParams = ref({});
const remoteUserInfo = ref(null);
const isCaller = ref(false);

// 初始化WebRTC管理器
const webrtc = new WebRTCManager();
webrtc.onRemoteStreamAdded = (stream) => {
	console.log('onRemoteStreamAdded')
  remoteStream.value = stream;
  isConnected.value = true;
  startDurationTimer();
};

// 响应式数据
const callStatus = ref('正在连接...');
const isConnected = ref(false);
const isMuted = ref(false);
const isTalking = ref(false);
const duration = ref(0);
const remoteStream = ref(null);
let durationTimer = null;

// 从路由获取参数
onLoad((options) => {
  // 解析路由参数
  routeParams.value = options;
  
  // 从路由参数中获取房间ID、对方用户信息和是否是主叫方
  const roomId = options.roomId || '';
  const userInfoStr = options.userInfo || '{}';
  const callerFlag = options.isCaller || 'false';
  
  try {
    remoteUserInfo.value = JSON.parse(decodeURIComponent(userInfoStr));
    isCaller.value = callerFlag === 'true';
    
    // 根据是否是主叫方设置初始状态
    callStatus.value = isCaller.value ? '呼叫中...' : '接听中...';
    
    // 初始化通话
    initCall(roomId);
  } catch (e) {
    console.error('解析路由参数失败:', e);
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
    setTimeout(() => uni.navigateBack(), 1500);
  }
});

// 初始化通话
const initCall = async (roomId) => {
  try {
    await webrtc.joinRoom(roomId, isCaller.value);
    
    // #ifdef APP-PLUS
    // 启动后台服务
    startBackgroundService();
    // #endif
  } catch (error) {
    console.error('通话初始化失败:', error);
    uni.showToast({
      title: '通话初始化失败',
      icon: 'none'
    });
    endCall();
  }
};

// 开始计时
const startDurationTimer = () => {
  callStatus.value = '通话中';
  durationTimer = setInterval(() => {
    duration.value += 1;
  }, 1000);
};

// 格式化时间显示
const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

// 按住说话
const startTalking = () => {
  if (!isConnected.value) return;
  isTalking.value = true;
  // 这里可以添加语音激活检测逻辑
};

const stopTalking = () => {
  isTalking.value = false;
};

// 切换静音
const toggleMute = () => {
  isMuted.value = !isMuted.value;
  if (webrtc.localStream) {
    webrtc.localStream.getAudioTracks().forEach(track => {
      track.enabled = !isMuted.value;
    });
  }
};

// 结束通话
const endCall = () => {
  clearInterval(durationTimer);
  webrtc.close();
  
  // #ifdef APP-PLUS
  // 关闭后台服务
  stopBackgroundService();
  // #endif
  
  uni.navigateBack();
};

// 清理资源
onUnmounted(() => {
  webrtc.close();
  clearInterval(durationTimer);
});

// ----------------------------
// 后台持续通话实现
// ----------------------------

// #ifdef APP-PLUS
// 启动后台服务
const startBackgroundService = () => {
  if (plus.os.name === 'Android') {
    const main = plus.android.runtimeMainActivity();
    const Intent = plus.android.importClass('android.content.Intent');
    const intent = new Intent(main.getIntent());
    intent.setClassName(main.getPackageName(), `${main.getPackageName()}.BackgroundVoiceService`);
    main.startService(intent);
  } else if (plus.os.name === 'iOS') {
    // iOS配置后台音频模式
    plus.ios.import('AVAudioSession').then(AVAudioSession => {
      const session = AVAudioSession.sharedInstance();
      session.setCategoryModeOptionsError(
        'AVAudioSessionCategoryPlayAndRecord',
        'AVAudioSessionModeVoiceChat',
        0x00000004 // AVAudioSessionCategoryOptionAllowBluetooth
      );
      session.setActiveError(true);
    });
  }
};

// 停止后台服务
const stopBackgroundService = () => {
  if (plus.os.name === 'Android') {
    const main = plus.android.runtimeMainActivity();
    const Intent = plus.android.importClass('android.content.Intent');
    const intent = new Intent(main.getIntent());
    intent.setClassName(main.getPackageName(), `${main.getPackageName()}.BackgroundVoiceService`);
    main.stopService(intent);
  }
};
// #endif
</script>

<style scoped>
.voice-call-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
}

.call-status {
  margin: 20px 0;
  font-size: 18px;
  color: #333;
}

.remote-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-bottom: 10px;
}

.name {
  font-size: 16px;
  color: #333;
}

.control-buttons {
  position: fixed;
  bottom: 50px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.talk-button {
  width: 80%;
  height: 50px;
  margin-bottom: 15px;
  background-color: #4CAF50;
  color: white;
  border-radius: 25px;
}

.mute-button {
  width: 80%;
  height: 40px;
  margin-bottom: 15px;
  background-color: #f0f0f0;
  border-radius: 20px;
}

.mute-button.active {
  background-color: #e0e0e0;
}

.end-call-button {
  width: 80%;
  height: 40px;
  background-color: #F44336;
  color: white;
  border-radius: 20px;
}
</style>