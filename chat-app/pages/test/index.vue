<template>
  <view class="container">
    <button @click="startCall">发起语音通话</button>
    <button @click="acceptCall">接听语音通话</button>
	<!-- <web-view v-if="show" id="wv" :src="src"></web-view> -->
  </view>
</template>

<script setup>
	import {
	  ref,
	  computed,
	  onMounted,
	  onUnmounted,
	  nextTick,
	  getCurrentInstance,
	  defineExpose,
	  watch
	} from "vue";

	const src=ref('')
	const show=ref(false)
const startCall = () => {
	show.value=true
	// src.value=`/hybrid/html/index.html?userId=${uni.getStorageSync("userId")}&isCaller=true&token=${uni.getStorageSync("token")}`
	
	// src.value=`http://localhost:5173/#/pages/test/voice-call?userId=${uni.getStorageSync("userId")}&isCaller=true&token=${uni.getStorageSync("token")}`
	// return
  uni.navigateTo({
    url: `/pages/test/voice-call?userId=${uni.getStorageSync("userId")}&isCaller=true`
	// url: `/pages/test/voice-call?remoteId=47&isCaller=true`
  });
};

const acceptCall = () => {
	show.value=true
	src.value=`/pages/test/voice-call?roomId=testroom&isCaller=false`
	return
  uni.navigateTo({
    url: `/pages/test/voice-call?roomId=testroom&isCaller=false`
  });
};

// 生成随机房间ID
const generateRoomId = () => {
  return Math.random().toString(36).substring(2, 10);
};
const message=(e)=>{
	console.log(e)
}
</script>

<style>
.container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>