<template>
	<view :style=" 'background-color: pink;' + {'height': height - 100 +'rpx'}">
		<!-- #ifdef H5 -->
		<!--H5场景专用，用户解决音频自动播放受限的问题-->
		<button v-if="isResumeAudio" 
			class="nertc-title-text"
			style=" margin-top: 20rpx; background-color: yellow; color: blue;" 
			@click="resumeAudio"
		>
		    点击恢复音频播放后会消失
		</button>
		<!-- #endif -->
		<!--本地视频画面的预览窗口-->
		<view :style = "'height:' + fullScreenHeight + 'px; position: relative; background-color:black;'">
			<!-- #ifdef APP-PLUS -->
			<nertc-local-view
				:style = "'height:' + fullScreenHeight + 'px;' + 'width:' + fullScreenWidth + 'px' "
				mediaType="video" 
				:viewID="userID">
			</nertc-local-view>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<div
				id="local_video"
				:style = "'height:' + fullScreenHeight + 'px;' + 'width:' + fullScreenWidth + 'px' "
			></div>
			<!-- #endif -->
		</view>
		<!-- 远端摄像头的渲染窗口 -->
		<view style="position: absolute; right: 10rpx; top:10rpx; width: 300rpx; height: 400rpx;" @click="changeck">
			<!-- #ifdef APP-PLUS -->
			<nertc-remote-view
				style="width: 300rpx; height: 400rpx"
				mediaType="video" 
				channel="small remote"
				v-if="remoteUserID"
				:userID="remoteUserID">
			</nertc-remote-view>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<div
			    v-bind:id="getRemoteVideoViewId(remoteUserID)"
			    style="position: absolute; top: 10rpx; right: 10rpx; width: 300rpx; height: 400rpx;"
			></div>
			<!-- #endif -->
		</view>
	
		<view style="position: absolute; left: 10rpx; top:10rpx;">
			<image style="width: 200rpx; height: 300rpx;"  v-if="ispreviewSnapshot" :src="base64Url">
			</image>
		</view>	
		<view v-if="isShowMoreButton" style="flex-direction: cooumn; flex-wrap: wrap; justify-content: center; position:fixed; bottom: 60px; right: 20px;">
			<button
				class="buttonStyle"
				style="width: 180rpx;"
				type="primary"
				@click="previewSnapshot"
			>
			  {{ ispreviewSnapshot ? "关闭" : "预览" }}截图
			</button>
			<button
				class="buttonStyle"
				style="width: 180rpx;"
				type="primary"
				v-if="remoteUserID"
				@click="takeRemoteSnapshot"
			>
			 远端截图
			</button>
			<button
				class="buttonStyle"
				style="width: 180rpx;"
				type="primary"
				@click="takeLocalSnapshot"
			>
			 本端截图
			</button>
			<button
				class="buttonStyle"
				style="width: 180rpx;"
				type="primary"
				@click="switchCamera"
			>
			 切换摄像头
			</button>
			<button
				style="width: 180rpx;"
				class="buttonStyle"
				type="primary"
				@click="enableBeauty"
			>
			 {{ isEnableBeauty ? "关闭" : "开启" }}美颜
			</button>
		</view>
		
		<view style="flex-direction: row; flex-wrap: wrap; justify-content: space-around; position:fixed; bottom: 10px;">
			<button
				class="buttonStyle"
				style="margin-left: 10rpx;"
				type="warn" 
				@click="createEngine"
			>
				{{ isSetup ? "销毁" : "创建" }}引擎
			</button>
			<button
			style="margin-left: 10rpx;"
				class="buttonStyle"
				type="warn" 
				@click="sendCallRequest"
			>
				发起通话
			</button>
			<button
			style="margin-left: 10rpx;"
				class="buttonStyle"
				type="warn" 
				@click="acceptCall"
			>
				接听通话
			</button>
			<button
			style="margin-left: 10rpx;"
				class="buttonStyle"
				type="warn" 
				@click="rejectCall"
			>
				拒绝通话
			</button>
			<button
			style="margin-left: 10rpx;"
				class="buttonStyle"
				type="warn" 
				@click="joinChannel"
			>
				{{ isLogin ? "退出" : "加入" }}房间
			</button>
			
			<button
				class="buttonStyle"
				style="margin-left: 10rpx;"
				type="primary"
				@click="openTheMic"
			>
				{{ isOpenAudio ? "关闭" : "打开" }}音频
			</button>
			
			<button
				class="buttonStyle"
				style="margin-left: 10rpx;"
				type="primary"
				@click="openTheCamera"
			>
				{{ isPublishingStream ? "关闭" : "打开" }}视频
			</button>
			<button
				class="buttonStyle"
				style="margin-left: 10rpx;width=130rpx"
				type="primary"
				@click="showMoreButton"
			>
				更多
			</button>
		</view>
	</view>
</template>

<script>
	import permision from "@/NERtcUniappSDK-JS/permission.js";
	import NERTC from "@/NERtcUniappSDK-JS/lib/index";
	import NertcLocalView from "@/NERtcUniappSDK-JS/nertc-view/NertcLocalView";
	import NertcRemoteView from "@/NERtcUniappSDK-JS/nertc-view/NertcRemoteView";
	import { 
		NERTCRenderMode,
		NERTCChannelConnectionState,
		NERTCMirrorMode, 
		NERtcVideoStreamType, 
		NERtcVideoFrameRate, 
		NERtcVideoCropMode,
		NERtcDegradationPreference,
		NERtcVideoOutputOrientationMode,
		NERtcVideoProfileType,
		NERtcRemoteVideoStreamType,
		NERTCAudioDevice,
		NERTCAudioDeviceType,
		NERTCAudioDeviceState,
		NERTCVideoDeviceState,
		NERTCConnectionType,
		NERTCErrorCode,
		NERtcAudioVolumeInfo,
		NERTCAudioProfile,
		NERTCAudioScenario,
		NERTCChannelProfile,
		NERTCUserRole,
		NERtcSubStreamContentPrefer,
		NERtcBeautyEffectType
	} from '@/NERtcUniappSDK-JS/lib/NERtcDefines';
	import keycenter from "@/pages/keyCenter.js"
	
	export default {
		components: {
			NertcRemoteView: NertcRemoteView,
		    NertcLocalView: NertcLocalView
		},
		data() {
			return {
				width: 0,
				height: 0,
				systemInfo: {},
				fullScreenHeight: 0,
				fullScreenWidth: 0,
				engine: undefined,
				channelName: keycenter.getChannelName(),
				userID: keycenter.getUserID(),
				remoteUserID: '',
				remoteBigAreaShow: false,
				localSmallAreaShow: false,
				mediaType:"video",
				channelProfile: NERTCChannelProfile.COMMUNICATION, //房间场景
				userRole: NERTCUserRole.CLIENT_ROLE_BROADCASTER, //主播
				isSetup: false, //是否初始化引擎
				isAutoSub: false, //自动订阅视频
				isLogin: false, //是否加入房间
				isSpeakerphoneOn: true, //默认扬声器
				isPublishingAudio: true, //默认发送音频
				isMuteAudio: false, //默认unmute
				isStopAudio: false, //默认发送
				isOpenAudio: true, //是否开启本地麦克风
				isPublishingStream: false, //是否开启本地摄像头
				isMuteVideo: false, //默认unmute
				isPublishingSubStream: false, //是否开启屏幕共享
				isPreviewVideo: false, //预览本地视频
				isPreviewScreen: false, //预览本地视频
				isAudioVolumeIndication: false, //提示说话者音量
				isStatsObserver: false, //本地通话统计信息
				isResumeAudio: false, //是否显示音频重新播放的按钮
				isShowMoreButton: false,
				isEnableBeauty: false,
				base64Url: '',
				ispreviewSnapshot: false,
				remoteId:'',
				token:''
			}
		},
		onShow() {
			
			console.log('onShow: 当 uni-app 启动，或从后台进入前台显示',this.userID);
		},
		onHide() {
			console.log('onHide: 当 uni-app 从前台进入后台');
		},
		onReady() {
			console.log('onReady: 监听页面初次渲染完成。注意如果渲染速度快，会在页面进入动画完成前触发');
		},
		onInit(data) {
			console.log('onInit: 监听页面初始化, 为上个页面传递的数据，触发时机早于 onLoad');
		},
		onLoad(data) {
			this.remoteId=data.remoteId
			console.log('onLoad: 监听页面初始化, 为上个页面传递的数据');
			this.systemInfo = uni.getSystemInfoSync();
			console.warn('systemInfo: ', JSON.stringify(this.systemInfo, null,  ' '))
			this.width = this.systemInfo.windowWidth;
			this.height = this.systemInfo.windowHeight;
			this.fullScreenHeight = this.height
			this.fullScreenWidth = this.width
			uni.$on('chat-message-received',async (message) => {
				console.log('收到新消息:', message);
				await this.handleServerMessage(message)
			})
			//setTimeout(this.createEngine(), 1000)
		},
		
		onUnload() {
			console.log('onUnload: 监听页面卸载');
		    this.destroyEngine();
		},
		onBackPress(event) {
			console.log('onBackPress: 监听页面返回, 返回 event = {from:backbutton、 navigateBack} ，backbutton 表示来源是左上角返回按钮或 android 返回键；navigateBack表示来源是 uni.navigateBack ');
		    this.destroyEngine();
		},
		methods: {
			// 发送普通消息
			sendToServer(message) {
			
				console.log('发送消息messagemessagemessagemessage', message)
				uni.sendSocketMessage({
					data: JSON.stringify(message)
				})
			
			},
			// 发送通话请求
			sendCallRequest() {
				this.sendToServer({
					typecode: 1,
					typecode2: 9,
					fromid: Number(this.userID),
					toid: Number(this.remoteId),
					msg: null
				})
			},
			
			
			
			
			// 接听来电
			async acceptCall() {
			
				this.sendToServer({
					typecode: 1,
					typecode2: 10,
					fromid: Number(this.userID),
					toid: Number(this.remoteId),
					// 0是接通  1是拒绝
					msg: JSON.stringify({
						apply: 0
					})
					// msg: encryptAESBase64(JSON.stringify({ apply: 0 }))
				})
			},

			async handleRoomAssignment(message) {
				try {
					// message.msg = JSON.parse(decryptAESBase64(message.msg))
					
					message.msg = JSON.parse(message.msg)
			
					this.channelName=message.msg.room
					this.token=message.msg.token
					console.log('分配房间',message)
					// 加入房间
			
				} catch (error) {
					console.error('房间处理错误:', error)
				}
			},
			handleIncomingCall(message) {
			  console.log(message)
	
			  this.remoteId = message.fromid;
			  console.log('收到通话请求',message, this.remoteId)
	
			},
			async handleServerMessage(message) {
				switch (message.typecode2) {
					case 9: // 通话请求
						this.handleIncomingCall(message)
						break
					case 10: // 通话响应
						this.handleCallResponse(message)
						break
					case 11: // 房间分配
						await this.handleRoomAssignment(message)
						break
			
					case 13: // 通话拒绝
						this.handleCallRejected(message)
						break
					default:
						console.warn('未知消息类型:', message)
				}
			},
			getRemoteVideoViewId(userID) {
				return 'video-' + userID
			},
			clear(){
				this.remoteUserID = '' //清除远端视频窗口
				this.channelProfile = NERTCChannelProfile.COMMUNICATION //房间场景
				this.userRole = NERTCUserRole.CLIENT_ROLE_BROADCASTER//主播
				this.isLogin = false//是否加入房间
				this.isSpeakerphoneOn = true//默认扬声器
				this.isPublishingAudio = true//默认发送音频
				this.isMuteAudio = false//默认unmute
				this.isStopAudio = false//默认发送
				this.isOpenAudio = true//是否开启本地麦克风
				this.isPublishingStream = false//是否开启本地摄像头
				this.isPublishingSubStream = false//是否开启屏幕共享
				this.isMuteVideo = false//默认unmute
				this.isPreviewVideo = false//预览本地视频
				this.isPreviewScreen = false//预览本地视频
				this.isAudioVolumeIndication = false
				this.isStatsObserver = false
				this.isAutoSub = false
				this.base64Url = ''
			},
			createEngine() {
				if(this.isSetup){
					this.destroyEngine()
					this.isSetup = false
					return
				}
				console.log('初始化NERTC引擎');
				this.engine = NERTC.setupEngineWithContext(keycenter.getEngineConfig());
				this.addEventListener()
				
				//加入房间后设置视频编码属性
				this.engine.setLocalVideoConfig(keycenter.getVideoConfig())
				console.log('初始化引擎完成，开始设置本地视频画布');
				this.engine.nertcPrint('初始化引擎完成，开始设置本地视频画布')
	
				
				// #ifdef APP-PLUS
					//判断权限
					if (uni.getSystemInfoSync().platform === "android") {
					    permision.requestAndroidPermission(
					        "android.permission.RECORD_AUDIO"
					    );
					    permision.requestAndroidPermission(
					        "android.permission.CAMERA"
					    );
					}
				// #endif
				
				this.isSetup = true
			},
			addEventListener(){
				//注册NERTC的事件
				this.engine.addEventListener("onError", (code, message, extraInfo) => {
					let imessage = `onError通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
					this.engine.nertcPrint(imessage)
					console.log(imessage)
					if (code === 41030) {
						imessage = `${extraInfo.userID} 音频播放收到浏览器限制需要用户手势触发`
						this.isResumeAudio = true
					}
					uni.showToast({
						title:imessage,
						icon: "none",
						duration: 3000
					})
				});
				this.engine.addEventListener("onWaring", (code, message, extraInfo) => {
					const imessage = `onWaring通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
					this.engine.nertcPrint(imessage)
					console.log(imessage)
					uni.showToast({
						title:imessage,
						icon: "none",
						duration: 3000
					})
				});
				this.engine.addEventListener("onJoinChannel", (result, channelId, elapsed, userID, userStringID) => {
					let message = `onJoinChannel通知：自己加入房间状况，result = ${result}, channelId = ${channelId}, elapsed = ${elapsed}, userID = ${userID}, userStringID = ${userStringID}`
					uni.showToast({
						title:'onJoinChannel结果: ' + result,
						icon: "none",
						duration: 1500
					})
					this.engine.nertcPrint(message)
					console.log(message)
					this.isPublishingStream = !this.isPublishingStream
					//加入房间后设置音频profile
					this.engine.setAudioProfile(keycenter.getAudioProfileConfig())
					
					//设置采集音量
					const volume = keycenter.getAudioVolumeConfig().captureVolume
					this.engine.adjustRecordingSignalVolume(volume)
					//设置整个房间的播放音量
					const channelPlaybackVolume = keycenter.getAudioVolumeConfig().channelPlaybackVolume
					this.engine.adjustChannelPlaybackSignalVolume(channelPlaybackVolume)
					
					const connectState = this.engine.getConnectionState()
					//获取链接状态
					message = `getConnectionState：获取链接状态，connectState = ${connectState}`
					this.engine.nertcPrint(message)
					console.log(message)
				});
				
				this.engine.addEventListener("onReconnectingStart", (result) => {
					const message = `onReconnectingStart通知：开始重连`
					this.engine.nertcPrint(message)
					console.log(message)
					uni.showToast({
						title: message,
						icon: "none",
						duration: 3000
					})
				});
				this.engine.addEventListener("onReJoinChannel", (result) => {
					const message = `onReJoinChannel通知：自己重新加入房间状况，result = ${result}`
					this.engine.nertcPrint(message)
					console.log(message)
					uni.showToast({
						title:'onReJoinChannel结果: ' + result,
						icon: "none",
						duration: 3000
					})
				});
				this.engine.addEventListener("onDisconnect", (reason) => {
					const message = `onDisconnect通知：断开reason = ${reason}`
					this.engine.nertcPrint(message)
					console.log(message)
					uni.showToast({
						title:'onDisconnect原因: ' + result,
						icon: "none",
						duration: 2000
					})
				});
				this.engine.addEventListener("onConnectionTypeChanged", (newConnectionType) => {
					const message = `onConnectionTypeChanged通知：newConnectionType=${newConnectionType}`
					this.engine.nertcPrint(message)
					console.log(message)
					uni.showToast({
						title:'网络类型变化为: ' + NERTCConnectionType[newConnectionType],
						icon: "none",
						duration: 3000
					})
				});
				this.engine.addEventListener("onConnectionStateChanged", (state, reason) => {
					const message = `onConnectionStateChanged通知：state=${state}, reason = ${reason}`
					this.engine.nertcPrint(message)
					console.log(message)
					uni.showToast({
						title:`网络变化：state=${state}, reason = ${reason}`,
						icon: "none",
						duration: 2000
					})
				});
				this.engine.addEventListener("onLeaveChannel", (result) => {
					const message = `onLeaveChannel通知：自己离开房间状况，result = ${result}`
					this.engine.nertcPrint(message)
					console.log(message)
					uni.showToast({
						title:'离开房间结果: ' + result,
						icon: "none"
					})
				});
				
				this.engine.addEventListener("onUserJoined", (userID, userStringID) => {
					const message = `onUserJoined通知：有人加入房间，userID = ${userID}, userStringID = ${userStringID}`
					this.engine.nertcPrint(message)
					console.log(message)
					uni.showToast({
						title:`${userID}加入房间`,
						icon: "none"
					})
				});
				
				this.engine.addEventListener("onUserLeave", (userID, reason, userStringID) => {
					const message = `onUserLeaved通知：有人离开房间，userID = ${userID}, userStringID = ${userStringID}, reason = ${reason}`
					this.engine.nertcPrint(message)
					console.log(message)
					//建议在当前页面上销毁 nertc-remote-view组件
					const remoteUserID = userStringID //`${userID}`
					if(this.remoteUserID == remoteUserID){
						//建议在当前页面上销毁 nertc-remote-view组件
						this.remoteUserID = '';
						uni.showToast({
							title:`${userStringID}离开房间`,
							icon: "none"
						})
						//点对点通话场景在对方离开房间后，自己也主动退出房间
						this.leaveChannel();
					}
					
				});
				
				this.engine.addEventListener("onUserAudioStart", ( userID, userStringID ) => {
					//sdk自动订阅对端音频，这里仅仅是通知信息，开发者不需要做什么逻辑
					const message = `onUserAudioStart通知：对方开启音频，userID = ${userID}, userStringID = ${userStringID}`
					this.engine.nertcPrint(message)
					console.log(message)
					this.isResumeAudio = true
				});
				
				this.engine.addEventListener("onUserAudioStop", (userID, userStringID) => {
					const message = `onUserAudioStop通知：对方关闭音频，userID = ${userID}, userStringID = ${userStringID}`
					this.engine.nertcPrint(message)
					console.log(message)
				});
				
				this.engine.addEventListener("onUserVideoStart", (userID, maxProfile, userStringID) => {
					const message = `onUserVideoStart通知：对方开启视频，userID = ${userID}, userStringID = ${userStringID}, maxProfile = ${maxProfile}`
					this.engine.nertcPrint(message)
					console.log(message)
					//nertc-remote-view组件 viewID和userID的数据格式是String类型，onUserVideoStart事件通知的userID是number，这里做一下数据格式转换
					const remoteUserID = userStringID //`${userID}`
					if (!this.remoteUserID) {
						this.remoteUserID = remoteUserID;
						//保证当前nertc-remote-view组件渲染完成，在执行设置画布的接口
						this.$nextTick(() => {
							console.warn('此时远端视频 nertc-remote-view 渲染完成')
							this.engine.nertcPrint('此时远端视频 nertc-remote-view 渲染完成')
							//需要开发者主动去做订阅对方视频的逻辑动作
							this.subscribeRemoteVideo(userID, userStringID) 
						})
					} else {
						//点对点通话只接受第一个人的视频，其他人忽略
						return
					}
				});
				
				this.engine.addEventListener("onUserVideoStop", (userID, userStringID) => {
					const message = `onUserVideoStop通知：对方关闭视频，userID = ${userID}, userStringID = ${userStringID}`
					this.engine.nertcPrint(message)
					console.log(message)
					//建议在当前页面上销毁对应的nertc-remote-view组件
					const remoteUserID = userStringID //`${userID}`
					if(this.remoteUserID == remoteUserID){
						//建议在当前页面上销毁 nertc-remote-view组件
						this.remoteUserID = '';
						//对端关闭视频，建议主动调用接口释放对端视频画布相关的资源
						this.engine.destroyRemoteVideoCanvas({
							userID,
							userStringID
						})
					}
				});
			},
			destroyEngine() {
				console.log('销毁引擎')
				this.engine?.nertcPrint('销毁引擎')
				this.engine?.removeAllEventListener()
				this.engine?.destroyEngine()
				this.engine = null
				this.clear()
				this.isSetup = false//是否初始化引擎
				this.isAutoSub = false
			},
			joinChannel(){
				if(!this.isSetup){
					console.log('当前没有初始化引擎，请稍后')
					return
				}
				if(this.isLogin){
					this.leaveChannel()
					this.isLogin = false
					return
				}
				console.log('加入房间: ', this.channelName, this.userID)
				this.engine.nertcPrint('加入房间')
				this.engine.joinChannel({
				    token: null,
				    channelName: this.channelName,
				    myUid: parseInt(this.userID),
					myStringUid: this.userID
				});
				this.isLogin = true
			},
			leaveChannel(){
				console.log('离开房间')
				this.engine.nertcPrint('离开房间')
				this.engine.leaveChannel();
				this.clear()
			},
			resumeAudio() {
				console.log('resumeAudio 恢复音频播放')
				if(this.engine){
					this.engine.rePlayAudio()
					this.isResumeAudio = !this.isResumeAudio
				}
			},
			openTheMic() {
				let message = 'openTheMic 停止音频: ' + this.isOpenAudio
				console.log(message)
				this.engine.nertcPrint(message)
				this.isOpenAudio = !this.isOpenAudio
				this.engine.enableLocalAudio(this.isOpenAudio) 
			},
			openTheCamera() {
				if(this.isPublishingStream){
					return this.closeTheCamera()
				}
				console.log('打开摄像头 openTheCamera')
				this.engine.nertcPrint('打开摄像头 openTheCamera')
				this.engine.enableLocalVideo({
					enable: true, //true表示设置启动摄像头，false表示关闭摄像头
					videoStreamType: 0 //0表示视频，1表示屏幕共享
				}) 
				this.isPublishingStream = true
			},
			closeTheCamera() {
				console.log('关闭摄像头 closeTheCamera')
				this.engine.nertcPrint('关闭摄像头 closeTheCamera')
				this.engine.enableLocalVideo({
					enable: false, //true表示设置启动摄像头，false表示关闭摄像头
					videoStreamType: 0 //0表示视频，1表示屏幕共享
				})
				this.isPublishingStream = false
			},
			switchCamera() {
				let message = '切换摄像头 switchCamera'
				if(!this.isPublishingStream) {
					console.log('切换摄像头：当前没有启动摄像头')
					this.engine.nertcPrint('切换摄像头 当前没有启动摄像头')
					return
				}
				console.log(message)
				this.engine.nertcPrint(message)
				this.engine.switchCamera()
			},
			takeLocalSnapshot() {
				console.log('本地截图 takeLocalSnapshot')
				this.engine.nertcPrint('本地截图 takeLocalSnapshot')
				this.engine.takeLocalSnapshot(NERtcVideoStreamType.MAIN).then(result=>{
					this.engine.nertcPrint('本地截图 返回结果: ' + JSON.stringify(result, null, ' '))
					console.log('本地截图 返回结果: ', JSON.stringify(result, null, ' '))
					if(result.img){
						this.base64Url = result.img
						//this.base64Url = this.base64Url.replace(/[\r\n]/g, "")
						//this.base64Url = "data:image/png;base64," + this.base64Url
						console.log('本地截图成功 base64Url: ', this.base64Url.length)
						this.engine.nertcPrint('本地截图成功 base64Url: ' + this.base64Url.length)
					} else {
						console.log('本地截图失败')
						this.engine.nertcPrint('本地截图失败')
					}
				})
			},
			takeRemoteSnapshot() {
				console.log('远端截图 takeRemoteSnapshot() remoteUserID: ', this.remoteUserID)
				this.engine.nertcPrint('远端截图 takeRemoteSnapshot')
				this.engine.takeRemoteSnapshot(
					{
						userID:this.remoteUserID - 0,
						userStringID: this.remoteUserID,
						videoStreamType: NERtcVideoStreamType.MAIN
					}).then(result=>{
					console.log('远端截图 takeRemoteSnapshot result: ', result)
					this.engine.nertcPrint('远端截图 takeRemoteSnapshot result: ' + result)
					if(result.img){
						console.log('远端截图成功')
						this.engine.nertcPrint('远端截图成功')
						this.base64Url = result.img
						//this.base64Url = this.base64Url.replace(/[\r\n]/g, "")
					} else {
						console.log('远端截图失败')
						this.engine.nertcPrint('远端截图失败')
					}
				})
			},
			previewSnapshot() {
				if (this.ispreviewSnapshot) {
					console.log('关闭截图 base64Url: ', this.base64Url.length)
					this.engine.nertcPrint('关闭截图 base64Url: ' + this.base64Url.length)
					this.ispreviewSnapshot = false
				} else {
					this.ispreviewSnapshot = true
					console.log('预览截图 base64Url: ', this.base64Url.length)
					this.engine.nertcPrint('预览截图 base64Url: ' + this.base64Url.length)
				}
			},
			changeck() {
				console.log('大小屏幕切换')
				this.engine.nertcPrint('[uniapp] 大小屏幕切换')
				this.engine.changeVideoCanvas({
					localVideoCanvasConfig: {
						renderMode: 2, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 //当前demo先使用数字，正式版本会是枚举
						mirrorMode: 2, //1表示启用镜像，2表示不启用
					},
					remoteVideoCanvasConfig: {
						renderMode: 1, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 //当前demo先使用数字，用户可以使用枚举
						mirrorMode: 1, //1表示启用镜像，2表示不启用
						userID: this.remoteUserID,
						userStringID: this.remoteUserID + ''
					}
				})
			},
			async subscribeRemoteVideo(remoteUserID, remoteUserStringID) {
				let messge = `开始拉流: remoteUserID=${remoteUserID}, remoteUserStringID=${remoteUserStringID}, 设置对端视频画布`
				console.log(messge)
				this.engine.nertcPrint(messge)
				// #ifdef H5
				// H5需要传入挂载节点
				const id = this.getRemoteVideoViewId(remoteUserID)
				console.warn('id: ', id)
				const view = document.getElementById(id)
				console.warn('view: ', view)
				this.engine.setupRemoteVideoCanvas({
					renderMode: 1, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 //当前demo先使用数字，用户可以使用枚举
					mirrorMode: 1, //1表示启用镜像，2表示不启用
					isMediaOverlay: true, //表示Z轴覆盖，总而言之，需要哪一端的视频画面在上面，则将哪一端的isMediaOverlay设置为true
					userID: remoteUserID,
					view
				})
				// #endif
				// #ifdef APP-PLUS
				this.engine.setupRemoteVideoCanvas({
					renderMode: 1, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 //当前demo先使用数字，用户可以使用枚举
					mirrorMode: 1, //1表示启用镜像，2表示不启用
					isMediaOverlay: true, //表示Z轴覆盖，总而言之，需要哪一端的视频画面在上面，则将哪一端的isMediaOverlay设置为true
					userID: remoteUserID,
					userStringID: remoteUserStringID
				})
				// #endif
				messge = `subscribeRemoteVideo: ${remoteUserID}, 订阅对端视频`
				console.log(messge)
				this.engine.nertcPrint(messge)
				this.engine.subscribeRemoteVideo({
					userID: remoteUserID,
					userStringID: remoteUserStringID,
					streamType: 0, //0表示大流，1表示小流
					subscribe: true //true表示订阅，false表示取消订阅
				})
			},
			//开启美颜
			enableBeauty() {
				console.log('enableBeauty() 开启美颜 isEnableBeauty: ', this.isEnableBeauty)
				if (this.isEnableBeauty) {
					this.isEnableBeauty = false
					//关闭美颜
					this.engine.stopBeauty()
				} else {
					//开启美颜
					this.isEnableBeauty = true
					this.engine.startBeauty()
					
					/** 设置滤镜 **/
					// #ifdef H5
						//H5端独有，设置滤镜效果
						/**
						 * 滤镜类型。包括：
						 * - `ziran`：自然。
						 * - `baixi`：白皙。
						 * - `fennen`：粉嫩。
						 * - `weimei`：唯美。
						 * - `langman`：浪漫。
						 * - `rixi`：日系。
						 * - `landiao`：蓝调。
						 * - `qingliang`：清凉。
						 * - `huaijiu`：怀旧。
						 * - `qingcheng`：青橙。
						 * - `wuhou`：午后。
						 * - `zhigan`：质感。
						 * - `mopian`：默片。
						 * - `dianying`：电影。
						 * - `heibai`：黑白。
						 *
						 */
						let type = 'baixi' //滤镜种类
						let level = 0.3 //滤镜强度
						this.engine.setFilter('baixi', 0.3)
						//设置为null，表示关闭滤镜
						//this.engine.setFilter(null)
					// #endif
					// #ifdef APP-PLUS
						//APP端独有，暂停美颜效果后，所有美颜效果都会暂时关闭，直至重新恢复美颜效果
						this.engine.enableBeauty(true) //false
						
						//APP设置滤镜需要加载本地滤镜资源文件
						let path = '2D/bunny' //支持 SD 卡上的绝对路径，或 asset 目录下的相对路径
						this.engine.addBeautyFilter(path);
						//关闭滤镜
						//this.engine.removeBeautyFilter()
						
					// #endif
					
					/*** 基础美颜相关配置 ***/
					
					//设置美白（推荐 0.6）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyWhiten"], 0.6)
					//设置磨皮（推荐 0.7）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautySmooth"], 0.7)
					//设置红润（推荐 0.5）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyFaceRuddy"], 0.5)
					//设置红润（推荐 0）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyFaceSharpen"], 0)
					
					/*** 高级美颜相关配置（H5目前(v5.6.32版本)没有实现） ***/
					
					/** 脸型	配置 **/
					//设置瘦脸（推荐 0.3）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyThinFace"], 0.3)
					//设置V脸（推荐 0）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyVFace"], 0.0)
					//设置窄脸（推荐 0）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyNarrowFace"], 0.0)
					//设置小脸（推荐 0.7）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautySmallFace"], 0.7)
					
					/** 面部 配置 **/
					//设置瘦颧骨（推荐 0）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyCheekBone"], 0.0)
					//设置瘦下颌（推荐 0.3）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyUnderJaw"], 0.0)
					//设置瘦下巴（推荐 0.5）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyJaw"], 0.5)
					//设置人中（推荐 0.5）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyPhiltrum"], 0.5)
					
					/** 眼睛 配置 **/
					
					//设置大眼（推荐 0.7）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyBigEye"], 0.7)
					//设置圆眼（推荐 0.85）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyRoundEye"], 0.85)
					//设置亮眼（推荐 0.6）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyLightEye"], 0.6)
					//设置开眼角（推荐 0）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyEyeCorner"], 0.0)
					//设置眼距调整（推荐 0.5）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyEyeDis"], 0.5)
					//设置眼角调整（推荐 0.5）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyEyeAngle"], 0.5)
					
					/** 鼻子 配置 **/
					
					//设置小鼻（推荐 0.5）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautySmallNose"], 0.5)
					//鼻长调整（推荐 0）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyLongNose"], 0.0)
					
					/** 嘴巴	 配置 **/
					
					//设置美牙（推荐 0.6）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyWhiteTeeth"], 0.6)
					//设置嘴角调整（推荐 0.5）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyMouthAngle"], 0.5)
					//设置嘴型调整（推荐 0.8）
					this.engine.setBeautyEffect(NERtcBeautyEffectType["kNERtcBeautyMouth"], 0.8)
				}
			},
			showMoreButton(){
				this.isShowMoreButton = this.isShowMoreButton ? false : true
			}
		}
	}
</script>

<style>
	.buttonStyle{
		width: 140rpx;
		height: 60rpx;
		margin-top: 20rpx;
		font-weight: 200;
	}
</style>