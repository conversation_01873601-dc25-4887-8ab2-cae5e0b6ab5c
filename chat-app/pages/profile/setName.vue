<template>
    <view class="apply-container">
        <view class="status_bar"></view>
        <view class="nav-bar">
            <view class="back-btn" @click="goBack">
                <image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
            </view>
			<view class="title">{{ $t('friendInfo.setRemark') }}</view>
            <button @click="setUserName" class="send-btn" size="mini" type="primary">{{ $t('common.save') }}</button>
        </view>
		
		<view class="apply-form">
		    <view class="desc">{{ $t('friendInfo.setRemark') }}</view>
		    <view class="input-section">
		        <up-textarea v-if="userInfo.type==2"  height="20" v-model="userInfo.Name" placeholder="" border="none"></up-textarea>
				<up-textarea v-if="userInfo.type==1" height="20" v-model="userInfo.name" placeholder="" border="none"></up-textarea>
		    </view>
		</view>
        
        <loading-tip :show="showLoading" :text="$t('common.loading')" bottom="30%"></loading-tip>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app'
import { addFriend, setFriend } from '@/api/friend'
import { setMeInfo } from '@/api/api'
import LoadingTip from '@/components/LoadingTip/index.vue'
import { t, getCurrentLanguage } from '@/utils/i18n.js';

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

const userInfo = ref({
	type:1, //1是自己  2是好友
})
const showLoading = ref(false)

onLoad((options) => {

    userInfo.value = JSON.parse(options.userInfo)
    console.log(options)
})

const applyMessage = ref('');
const remark = ref('');

const goBack = () => {
    uni.navigateBack();
};
const setUserName =async ()=>{
	console.log(userInfo.value)
	showLoading.value = true
	if(userInfo.value.type==2){
		const {data: res} = await setFriend({id: Number(userInfo.value.Friend),name:userInfo.value.Name})
		if(res.code==0){
			uni.$emit('update',{type:'contacts'})
			setTimeout(() => {
			    showLoading.value = false
			    uni.navigateBack()
			}, 1500)
		}else{
			showLoading.value = false
			uni.showToast({
			    title: $t('common.failed'),
			    icon: 'none'
			})
		}
		console.log(res)
	}
	if(userInfo.value.type==1){
		const {data: res} = await setMeInfo({name:userInfo.value.name,header_img:userInfo.value.head_img,})
		if(res.code==0){
			
			uni.setStorageSync("userInfo", userInfo.value);
			setTimeout(() => {
			    showLoading.value = false
			    uni.navigateBack()
			}, 1500)
		}else{
			showLoading.value = false
			uni.showToast({
			    title: '设置失败',
			    icon: 'none'
			})
		}
	}
}
const sendApply = async () => {
    try {
        showLoading.value = true
        const params = {
            id: Number(userInfo.value.id),
            text: applyMessage.value,
        }
        const setParams = {
            id: Number(userInfo.value.id),
            name: remark.value
        }
        const {data: res} = await addFriend(params)
        // const {data: setRes} = await setFriend(setParams)
        if(res.code == 0 && setRes.code == 0) {
            setTimeout(() => {
                showLoading.value = false
                uni.navigateBack()
            }, 1500)
        } else {
            showLoading.value = false
            uni.showToast({
                title: '添加好友失败',
                icon: 'none'
            })
        }
    } catch (error) {
        console.log(error)
        showLoading.value = false
        uni.showToast({
            title: '添加好友失败',
            icon: 'none'
        })
    }
};
</script>

<style lang="scss" scoped>
::v-deep .nav-bar{
    border-bottom: none !important; 
	
}
::v-deep .u-textarea{
    background-color: #f8f8f8 !important;
}
::v-deep .u-input{
    background-color: #f8f8f8 !important;
}
.apply-container {
    min-height: 100vh;
    background-color: #fff;
}

.status_bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: #fff;
}

.nav-bar {
    position: relative;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    
}

.back-btn {
    position: absolute;
    left: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    color: #FF3366;
}

.arrow-left {
    width: 26rpx;
    height: 26rpx;
    transform: rotate(180deg);
}

.title {
    font-size: 32rpx;
    color: #FF3366;
    text-align: center;
    font-weight: 500;
}

.apply-form {
    padding: 30rpx;
}

.desc {
    padding-left: 20rpx;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 20rpx;
}

.input-section {
    background-color: #fff;
    border-radius: 8rpx;
    margin-bottom: 40rpx;
}

.send-btn-wrapper {
    padding: 0 30rpx;
    margin-top: 660rpx;
}

.send-btn {
	position: absolute;
	right: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1;
	background-color: #FD3357;
}

.send-btn:active {
    opacity: 0.9;
}

:deep(.u-input) {
    background-color: #fff;
    padding: 0;
}
</style>